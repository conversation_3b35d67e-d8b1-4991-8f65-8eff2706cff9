from src.wecom_bot_svr import WecomBotServer
from src.wecom_bot import msg_handler, event_handler

def main():
    # URL 填写的URL需要正确响应企业微信验证URL的请求
    # http://api-idc.sgw.woa.com/ebus/yyb_ai/wecom_bot
    # http://api-idc.sgw.woa.com/ebus/version_wecom_bot_test/version_wecom_bot_zzy

    # zhiyi
    # token = 'P5deC3S1x3TPnV5hCY0jwrG'
    # aes_key = 'MoivtzUTfH9F3giwBXuP03zisACFC8HYsxtNCoLzOXz'
    # bot_key = 'eff28a6c-45e4-4e8e-b8d8-0cd345598a9b'  
    # bot_name = 'version_bot_test'
    # path='/version_wecom_bot_zzy'

    #lichen
    token = '4rwXKaSJ9Tvh2rboLAsAJ5P'
    aes_key = 'xbjt1vsILWYRtF7R9emM5nwt58dMMUJFaOC9pxzvlIF'
    bot_key = '91e2f920-ca4a-4be0-a6d4-d29e680b80cb'
    bot_name = '应用宝终端AI小助手'
    path='/log_wecom_bot'

    corp_id = ''
    host = '0.0.0.0'
    port = 5001
  
    server = WecomBotServer(name=bot_name, host=host, port=port, path=path, token=token, aes_key=aes_key, corp_id=corp_id,
                            bot_key=bot_key)

    server.set_message_handler(msg_handler)
    server.set_event_handler(event_handler)
    server.run()


if __name__ == '__main__':
    main()
