        ⚠️  没有CVR数据，使用占位符
       处理日期 [2/3]: 2025/06/07
         格式化日期: 2025/06/07 -> 2025/6/7
         ⚠️  没有下载数据，使用占位符
         ⚠️  没有CVR数据，使用占位符
       处理日期 [3/3]: 2025/06/08
         格式化日期: 2025/06/08 -> 2025/6/8
         ⚠️  没有下载数据，使用占位符
         ⚠️  没有CVR数据，使用占位符
     处理QUA [73/74]: /
       ⚠️  占位符版本，使用默认数据
     处理QUA [74/74]: 3370.00
       处理日期 [1/3]: 2025/06/06
         格式化日期: 2025/06/06 -> 2025/6/6
         ⚠️  没有下载数据，使用占位符
         ⚠️  没有CVR数据，使用占位符
       处理日期 [2/3]: 2025/06/07
         格式化日期: 2025/06/07 -> 2025/6/7
         ⚠️  没有下载数据，使用占位符
         ⚠️  没有CVR数据，使用占位符
       处理日期 [3/3]: 2025/06/08
         格式化日期: 2025/06/08 -> 2025/6/8
         ⚠️  没有下载数据，使用占位符
         ⚠️  没有CVR数据，使用占位符
     🔍 进行下载数据实验组与对照组比较...
       📋 对照组索引: 73 (版本: 3370.00)
       处理实验组1 (索引0):
         第1天开始下载率: 0% (⚠️ 对照组数据缺失, 0户)
         第1天成功下载率: 0%
         第1天CVR: 53.61% (⚠️ 对照组数据缺失, 13563数)
         第2天开始下载率: 72.60% (⚠️ 对照组数据缺失, 424户)
         第2天成功下载率: 90.80%
         第2天CVR: 58.86% (⚠️ 对照组数据缺失, 31865数)
         第3天开始下载率: 71.51% (⚠️ 对照组数据缺失, 7759户)
         第3天成功下载率: 83.95%
         第3天CVR: 56.84% (⚠️ 对照组数据缺失, 46823数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理实验组2 (索引1):
         第1天开始下载率: 0% (⚠️ 对照组数据缺失, 0户)
         第1天成功下载率: 0%
         第1天CVR: 55.99% (⚠️ 对照组数据缺失, 13445数)
         第2天开始下载率: 72.28% (⚠️ 对照组数据缺失, 412户)
         第2天成功下载率: 90.53%
         第2天CVR: 57.09% (⚠️ 对照组数据缺失, 30802数)
         第3天开始下载率: 72.25% (⚠️ 对照组数据缺失, 8017户)
         第3天成功下载率: 84.08%
         第3天CVR: 58.01% (⚠️ 对照组数据缺失, 45603数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理实验组3 (索引2):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引3):
         第1天开始下载率: 0% (占位符组, 0户)
         第1天成功下载率: 0%
         第1天CVR: 55.02% (占位符组, 13823数)
         第2天开始下载率: 67.00% (占位符组, 406户)
         第2天成功下载率: 89.90%
         第2天CVR: 57.12% (占位符组, 32398数)
         第3天开始下载率: 71.95% (占位符组, 7938户)
         第3天成功下载率: 83.66%
         第3天CVR: 56.32% (占位符组, 46063数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引4):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引5):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引6):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引7):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引8):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引9):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引10):
         第1天开始下载率: 0% (占位符组, 0户)
         第1天成功下载率: 0%
         第1天CVR: 53.61% (占位符组, 13563数)
         第2天开始下载率: 72.60% (占位符组, 424户)
         第2天成功下载率: 90.80%
         第2天CVR: 58.86% (占位符组, 31865数)
         第3天开始下载率: 71.51% (占位符组, 7759户)
         第3天成功下载率: 83.95%
         第3天CVR: 56.84% (占位符组, 46823数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引11):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引12):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引13):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引14):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引15):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引16):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引17):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引18):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引19):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引20):
         第1天开始下载率: 0% (占位符组, 0户)
         第1天成功下载率: 0%
         第1天CVR: 53.61% (占位符组, 13563数)
         第2天开始下载率: 72.60% (占位符组, 424户)
         第2天成功下载率: 90.80%
         第2天CVR: 58.86% (占位符组, 31865数)
         第3天开始下载率: 71.51% (占位符组, 7759户)
         第3天成功下载率: 83.95%
         第3天CVR: 56.84% (占位符组, 46823数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引21):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引22):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引23):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引24):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引25):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引26):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引27):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引28):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引29):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引30):
         第1天开始下载率: 0% (占位符组, 0户)
         第1天成功下载率: 0%
         第1天CVR: 53.61% (占位符组, 13563数)
         第2天开始下载率: 72.60% (占位符组, 424户)
         第2天成功下载率: 90.80%
         第2天CVR: 58.86% (占位符组, 31865数)
         第3天开始下载率: 71.51% (占位符组, 7759户)
         第3天成功下载率: 83.95%
         第3天CVR: 56.84% (占位符组, 46823数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引31):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引32):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引33):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引34):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引35):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引36):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引37):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引38):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引39):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引40):
         第1天开始下载率: 0% (占位符组, 0户)
         第1天成功下载率: 0%
         第1天CVR: 53.61% (占位符组, 13563数)
         第2天开始下载率: 72.60% (占位符组, 424户)
         第2天成功下载率: 90.80%
         第2天CVR: 58.86% (占位符组, 31865数)
         第3天开始下载率: 71.51% (占位符组, 7759户)
         第3天成功下载率: 83.95%
         第3天CVR: 56.84% (占位符组, 46823数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引41):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引42):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引43):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引44):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引45):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引46):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引47):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引48):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引49):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引50):
         第1天开始下载率: 0% (占位符组, 0户)
         第1天成功下载率: 0%
         第1天CVR: 53.61% (占位符组, 13563数)
         第2天开始下载率: 72.60% (占位符组, 424户)
         第2天成功下载率: 90.80%
         第2天CVR: 58.86% (占位符组, 31865数)
         第3天开始下载率: 71.51% (占位符组, 7759户)
         第3天成功下载率: 83.95%
         第3天CVR: 56.84% (占位符组, 46823数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引51):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引52):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引53):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引54):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引55):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引56):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引57):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引58):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引59):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引60):
         第1天开始下载率: 0% (占位符组, 0户)
         第1天成功下载率: 0%
         第1天CVR: 53.61% (占位符组, 13563数)
         第2天开始下载率: 72.60% (占位符组, 424户)
         第2天成功下载率: 90.80%
         第2天CVR: 58.86% (占位符组, 31865数)
         第3天开始下载率: 71.51% (占位符组, 7759户)
         第3天成功下载率: 83.95%
         第3天CVR: 56.84% (占位符组, 46823数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引61):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引62):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引63):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引64):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引65):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引66):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引67):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引68):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引69):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引70):
         第1天开始下载率: 0% (占位符组, 0户)
         第1天成功下载率: 0%
         第1天CVR: 53.61% (占位符组, 13563数)
         第2天开始下载率: 72.60% (占位符组, 424户)
         第2天成功下载率: 90.80%
         第2天CVR: 58.86% (占位符组, 31865数)
         第3天开始下载率: 71.51% (占位符组, 7759户)
         第3天成功下载率: 83.95%
         第3天CVR: 56.84% (占位符组, 46823数)
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引71):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引72):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
       处理对照组 (索引73):
         第1天: 下载数据缺失
         第1天CVR: 数据缺失
         第2天: 下载数据缺失
         第2天CVR: 数据缺失
         第3天: 下载数据缺失
         第3天CVR: 数据缺失
         第4天: 下载数据缺失
         第4天CVR: 数据缺失
     ✅ 下载数据收集完成
  🎯 收集其他数据 (广告、联网、弹窗、云游戏):
     日期范围: 2025-06-06 ~ 2025-06-08
     QUA版本列表: ['TMAF_899_P_8549', 'TMAF_899_P_8551', '/', 'TMAF_899_P_8552', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00']
     有效QUA版本: ['TMAF_899_P_8549', 'TMAF_899_P_8551', 'TMAF_899_P_8552', 'QUA', '2821.51', '2729.19', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '3370.00', 'QUA', '2821.51', '2729.19', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '3370.00', 'QUA', '2821.51', '2729.19', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '3370.00', 'QUA', '2821.51', '2729.19', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '3370.00', 'QUA', '2821.51', '2729.19', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '3370.00', 'QUA', '2821.51', '2729.19', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '3370.00', 'QUA', '2821.51', '2729.19', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '3370.00']
     RQD版本列表: ['8.9.9_8994130_8549', '8.9.9_8994130_8551', '/', '8.9.9_8994130_8552', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '/', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '/', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '/', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '/', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '/', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '/', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '/', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '/', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '/', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '/', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '/', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '/', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '/', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '/', '4761.69']
     有效RQD版本: ['8.9.9_8994130_8549', '8.9.9_8994130_8551', '8.9.9_8994130_8552', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '4761.69', '常规热启动<br>(gap<50ms)', '2544.44', '<span style="color: red;">3567.63<br>(163次)</span>', '2865.86', '常规外call热启动<br>(gap<50ms)', '2153.23', '3072.76', '4761.69']
     📡 查询广告数据...
     ✅ 获取到广告数据，包含 3 个日期
       🔍 验证广告数据对齐...
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-06, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-06, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-06, QUA=QUA
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-07, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-07, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-07, QUA=QUA
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-08, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-08, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-08, QUA=QUA
         📊 总计缺失数据点: 21
         🔍 进行广告数据实验组与对照组比较...
         📋 对照组索引: 73 (版本: 3370.00)
         处理实验组1 (索引0):
           处理日期: 2025/6/6
             ⚠️ 对照组数据缺失，无法比较
             曝光=1202199, 点击=3302, 下载=1180, 安装=1326
           处理日期: 2025/6/7
             ⚠️ 对照组数据缺失，无法比较
             曝光=3850909, 点击=8790, 下载=2952, 安装=3089
           处理日期: 2025/6/8
             ⚠️ 对照组数据缺失，无法比较
             曝光=6053922, 点击=13130, 下载=4722, 安装=4931
         处理实验组2 (索引1):
           处理日期: 2025/6/6
             ⚠️ 对照组数据缺失，无法比较
             曝光=1213776, 点击=2877, 下载=1140, 安装=1327
           处理日期: 2025/6/7
             ⚠️ 对照组数据缺失，无法比较
             曝光=3811969, 点击=8187, 下载=2805, 安装=3063
           处理日期: 2025/6/8
             ⚠️ 对照组数据缺失，无法比较
             曝光=6023704, 点击=12236, 下载=4208, 安装=4674
         处理实验组3 (索引2):
           占位符组，使用默认数据
         处理对照组 (索引3):
           处理日期: 2025/6/6
             点击曝光率: 0.25% (占位符组)
             下载点击率: 37.36%
             安装下载率: 110.55%
             曝光=1240978, 点击=3145, 下载=1175, 安装=1299
           处理日期: 2025/6/7
             点击曝光率: 0.22% (占位符组)
             下载点击率: 34.18%
             安装下载率: 107.50%
             曝光=3892179, 点击=8465, 下载=2893, 安装=3110
           处理日期: 2025/6/8
             点击曝光率: 0.21% (占位符组)
             下载点击率: 33.79%
             安装下载率: 108.43%
             曝光=6047081, 点击=12888, 下载=4355, 安装=4722
         处理对照组 (索引4):
     ❌ 获取广告数据失败: list index out of range
     📡 查询联网数据...
     ✅ 获取到联网数据，包含 3 个日期
       🔍 验证联网数据对齐...
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-06, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-06, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-06, QUA=QUA
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-07, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-07, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-07, QUA=QUA
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-08, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-08, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-08, QUA=QUA
         📊 总计缺失数据点: 21
         处理日期: 2025/6/6
           QUA TMAF_899_P_8549: 联网用户数=448452
           QUA TMAF_899_P_8551: 联网用户数=448053
           QUA /: 无数据，使用占位符
           QUA TMAF_899_P_8552: 联网用户数=448397
           QUA QUA: 无数据，使用占位符
     ❌ 获取联网数据失败: list index out of range
     📡 查询弹窗数据...
     ✅ 获取到弹窗数据，包含 3 个日期
       🔍 验证弹窗数据对齐...
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-06, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-06, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-06, QUA=QUA
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-07, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-07, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-07, QUA=QUA
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2143.46
         ⚠️  缺失数据: 日期=2025-06-08, QUA=3370.00
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2148.17
         ⚠️  缺失数据: 日期=2025-06-08, QUA=常规冷启动<br>(gap<100ms)
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2729.19
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2821.51
         ⚠️  缺失数据: 日期=2025-06-08, QUA=QUA
         📊 总计缺失数据点: 21
         🔍 进行弹窗数据实验组与对照组比较...
         📋 对照组索引: 73 (版本: 3370.00)
         处理实验组1 (索引0):
           处理日期: 2025/6/6
             弹窗成功率: 81.54% (⚠️ 对照组数据缺失，无法比较)
           处理日期: 2025/6/7
             弹窗成功率: 88.50% (⚠️ 对照组数据缺失，无法比较)
           处理日期: 2025/6/8
             弹窗成功率: 90.22% (⚠️ 对照组数据缺失，无法比较)
         处理实验组2 (索引1):
           处理日期: 2025/6/6
             弹窗成功率: 81.63% (⚠️ 对照组数据缺失，无法比较)
           处理日期: 2025/6/7
             弹窗成功率: 88.55% (⚠️ 对照组数据缺失，无法比较)
           处理日期: 2025/6/8
             弹窗成功率: 90.23% (⚠️ 对照组数据缺失，无法比较)
         处理实验组3 (索引2):
           占位符组，使用默认数据
         处理对照组 (索引3):
           处理日期: 2025/6/6
             弹窗成功率: 81.53% (占位符组)
           处理日期: 2025/6/7
             弹窗成功率: 88.43% (占位符组)
           处理日期: 2025/6/8
             弹窗成功率: 90.23% (占位符组)
         处理对照组 (索引4):
     ❌ 获取弹窗数据失败: list index out of range
     📡 查询云游戏数据...
     ✅ 获取到云游戏数据，包含 3 个日期
       🔍 验证云游戏数据对齐...
         ⚠️  缺失数据: 日期=2025-06-06, QUA=<span style="color: red;">3567.63<br>(163次)</span>
         ⚠️  缺失数据: 日期=2025-06-06, QUA=4761.69
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2153.23
         ⚠️  缺失数据: 日期=2025-06-06, QUA=3072.76
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2865.86
         ⚠️  缺失数据: 日期=2025-06-06, QUA=常规热启动<br>(gap<50ms)
         ⚠️  缺失数据: 日期=2025-06-06, QUA=常规外call热启动<br>(gap<50ms)
         ⚠️  缺失数据: 日期=2025-06-06, QUA=2544.44
         ⚠️  缺失数据: 日期=2025-06-07, QUA=<span style="color: red;">3567.63<br>(163次)</span>
         ⚠️  缺失数据: 日期=2025-06-07, QUA=4761.69
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2153.23
         ⚠️  缺失数据: 日期=2025-06-07, QUA=3072.76
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2865.86
         ⚠️  缺失数据: 日期=2025-06-07, QUA=常规热启动<br>(gap<50ms)
         ⚠️  缺失数据: 日期=2025-06-07, QUA=常规外call热启动<br>(gap<50ms)
         ⚠️  缺失数据: 日期=2025-06-07, QUA=2544.44
         ⚠️  缺失数据: 日期=2025-06-08, QUA=<span style="color: red;">3567.63<br>(163次)</span>
         ⚠️  缺失数据: 日期=2025-06-08, QUA=4761.69
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2153.23
         ⚠️  缺失数据: 日期=2025-06-08, QUA=3072.76
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2865.86
         ⚠️  缺失数据: 日期=2025-06-08, QUA=常规热启动<br>(gap<50ms)
         ⚠️  缺失数据: 日期=2025-06-08, QUA=常规外call热启动<br>(gap<50ms)
         ⚠️  缺失数据: 日期=2025-06-08, QUA=2544.44
         📊 总计缺失数据点: 24
         🔍 进行云游戏数据阈值检测 (≤97%标红)...
         处理日期: 2025-06-06
           RQD 8.9.9_8994130_8549: 云游插件拉起成功率=99.88% (✅ 正常)
           RQD 8.9.9_8994130_8551: 云游插件拉起成功率=99.69% (✅ 正常)
           RQD /: 无数据，使用占位符
           RQD 8.9.9_8994130_8552: 云游插件拉起成功率=99.92% (✅ 正常)
           RQD 常规热启动<br>(gap<50ms): 无数据，使用占位符
     ❌ 获取云游戏数据失败: list index out of range
     ✅ 其他数据收集完成
✅ 老用户数据收集完成

✅ 所有数据收集完成

🔍 验证最终数据一致性...
==================================================
❌ 发现数据一致性问题:
   1. 老用户QUA版本不一致: 期望=['TMAF_899_P_8549', 'TMAF_899_P_8551', '/', 'TMAF_899_P_8552', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00', 'QUA', '2821.51', '2729.19', '/', '2143.46', '常规冷启动<br>(gap<100ms)', 'TMAF_899_P_8549', '2148.17', '/', '3370.00'], 实际=['TMAF_899_P_8549', 'TMAF_899_P_8551', '/', 'TMAF_899_P_8552']
   2. 老用户设备crash率数据结构错误: 应为4组，实际为74组
   3. 老用户ANR率数据结构错误: 应为4组，实际为74组
   4. 老用户下载开始率数据结构错误: 应为4组，实际为74组
   5. 老用户常规热启动数据错误: 应为4组，实际为74组
   6. 老用户常规冷启动数据错误: 应为4组，实际为74组

总计发现 6 个问题

============================================================
🎉 灰度数据收集完成！
============================================================
📊 数据收集总结:
  ✅ 新用户设备crash率: 4组 x 4天
  ✅ 老用户设备crash率: 74组 x 4天
  ✅ 新用户启动速度: 4组
  ✅ 老用户启动速度: 74组
  ✅ 新用户下载数据: 4组 x 4天
  ✅ 老用户下载数据: 74组 x 4天
  ✅ 新用户广告数据: 4组 x 4天
  ✅ 老用户广告数据: 4组 x 4天

📋 数据样例验证:
  新用户QUA版本: ['TMAF_899_P_8547', 'TMAF_899_P_8548', '/', 'TMAF_899_P_8550']
  老用户QUA版本: ['TMAF_899_P_8549', 'TMAF_899_P_8551', '/', 'TMAF_899_P_8552']
  新用户测试日期: ['2025/06/06', '2025/06/07', '2025/06/08', '/']
  老用户测试日期: ['2025/06/06', '2025/06/07', '2025/06/08', '/']
  新用户crash率样例: ['0.10%', '0.12%']...
  新用户启动速度样例: 热启动=['2120.91', '2170.07']...

📈 数据质量报告:
  数据一致性检查: ❌ 失败
  ⚠️  建议检查数据收集过程中的警告信息
🔍 验证模板数据映射...
✅ 模板数据映射验证通过

📄 生成报告内容...
🔍 验证模板数据映射...
✅ 模板数据映射验证通过
result: {'success': True, 'request_id': '412d634e-8cae-4ff6-b918-e20d74ad70b6', 'message': '文档内容追加成功', 'raw_response': {'code': 'Ok', 'msg': 'ok', 'request_id': '412d634e-8cae-4ff6-b918-e20d74ad70b6'}}
🔗 访问链接: https://iwiki.woa.com/p/4015180169

✅ 数据收集完成
