# 应用宝终端AI小助手

## 项目简介

应用宝终端AI小助手是一个基于企业微信的智能机器人服务，专为应用宝团队提供日志分析、版本管理、灰度数据分析等自动化服务。通过自然语言交互，用户可以快速获取日志分析结果、版本信息查询、满意度回访等功能。

## 项目结构
```
logassistant-server/
├── main.py                 # 项目入口文件
├── requirements.txt        # 依赖配置
├── setup.py               # 安装配置
├── .gitignore             # Git忽略配置
├── logs/                  # 日志存储目录
├── .log/                  # 系统日志目录
└── src/                   # 源代码包
    ├── __init__.py        # 包初始化文件
    ├── wecom_bot.py       # 企微消息处理器
    ├── modules/           # 业务逻辑处理器
    │   ├── version_tool/  # 版本相关工具集合
    │   │   ├── gray_data/     # 灰度数据分析
    │   │   ├── version_mr/    # 版本需求管理
    │   │   └── version_info/  # 版本信息查询
    │   ├── log_tool/      # 日志分析工具
    │   └── other_reply/   # 其他回复处理
    ├── client/            # 外部服务客户端
    ├── common/            # 通用工具和基础设施
    │   ├── error/         # 错误处理
    │   ├── constant/      # 常量定义
    │   ├── logs/          # 日志系统
    │   ├── models/        # 数据模型
    │   ├── wecom/         # 企微相关工具
    │   └── tools/         # 通用工具
    ├── function_call/     # 意图识别
    ├── services/          # 业务服务层
    │   └── request_dispatcher.py  # 请求分发器
    ├── slot_filter/       # 参数抽取
    └── wecom_bot_svr/     # 企微协议框架
```

## 安装部署

### 环境要求
- Python 3.7+
- pip

### 安装步骤

1. **创建虚拟环境**
```bash
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# 或
.venv\Scripts\activate  # Windows
```

2. **安装依赖**

由于部分依赖包需要特定的镜像源，建议按以下顺序安装：

```bash
pip3 install requests setuptools rapidfuzz>=3.0.0
pip3 install polaris-cpp-py --index-url https://mirrors.cloud.tencent.com/pypi/simple/
pip3 install sseclient-py==1.7.2 -i https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple --extra-index-url https://mirrors.tencent.com/pypi/simple/
pip3 install tapd-python-sdk -i https://mirrors.tencent.com/repository/pypi/tencent_pypi/simple --extra-index-url https://pypi.python.org/simple/
pip3 install wecom_bot_svr tokenizers openpyxl
pip3 install apscheduler
```

3. **配置企微机器人**
在 `main.py` 中配置你的企微机器人参数：
```python
token = 'your_token'
aes_key = 'your_aes_key'
bot_key = 'your_bot_key'
corp_id = 'your_corp_id'
bot_name = '你的机器人名称'
```
在 `src/common/wecom/wecom_bot_tips.py`中配置webhook的路径
```python
WEBHOOK_KEY = 'your_webhook_key'  
```

4. **运行服务**
```bash
python main.py
```
