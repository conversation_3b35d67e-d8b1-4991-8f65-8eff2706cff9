import requests
import hashlib
import time
import random
from urllib.parse import urlparse
from typing import Optional, List, Dict, Union, Any
from datetime import datetime
import re
import json
from src.common.error.detailed_value_error import raise_value_error, ErrorCode, DetailedValueError
from src.common.tools.text_parse_utils import TextParseUtils

class IWikiClient:
    """
    IWiki API，用于获取文档内容、创建文档和追加文档内容。
    调用文档：https://iwiki.woa.com/p/36307200
    swagger文档：https://iwiki.woa.com/p/4008410284
    """

    # 云研发需要选择下面这个 BASE_URL
    BASE_URL = "http://api-idc.sgw.woa.com/ebus/iwiki/prod/tencent/"
    DOC_BODY_PATH = "api/v2/doc/body"
    DOC_CREATE_PATH = "api/v2/doc/create"
    DOC_APPEND_PATH = "api/v2/doc/save/parts"
    DOC_UPDATE_PATH = "api/v2/doc/save"
    VIKA_RECORDS_PATH = "api/vika/third/records"
    VIKA_FIELDS_PATH = "api/vika/third/fields"

    def __init__(self, paas_id: str = 'yyb_ai', 
                    paas_token: str = '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN', 
                    server: str = "http://api-idc.sgw.woa.com"):
        """
        初始化客户端

        Args:
            paas_id: 太湖注册获得的应用id
            paas_token: 太湖注册获得的签名密钥
            server: 智能网关接入点域名，默认值为DevCloud区域
        """
        self.paas_id = paas_id
        self.paas_token = paas_token
        self.server = server.rstrip('/')
        self.session = requests.Session()

    def _generate_signature(self, timestamp: str, nonce: str) -> str:
        """
        生成请求签名

        Args:
            timestamp: 时间戳字符串
            nonce: 随机字符串

        Returns:
            str: 大写的sha256签名字符串
        """
        raw_string = timestamp + self.paas_token + nonce + timestamp
        signature = hashlib.sha256(raw_string.encode()).hexdigest().upper()
        return signature

    def _prepare_common_headers(self) -> Dict[str, str]:
        """
        准备通用请求头

        :return: 包含认证信息的请求头字典
        """
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = self._generate_signature(timestamp, nonce)

        return {
            'Content-Type': 'application/json',
            'x-rio-paasid': self.paas_id,
            'x-rio-timestamp': timestamp,
            'x-rio-nonce': nonce,
            'x-rio-signature': signature
        }

    def _build_url(self, path: str) -> str:
        """
        构建完整的API URL

        :param path: API路径
        :return: 完整的URL
        """
        return f"{self.server}/ebus/iwiki/prod/tencent/{path}"


    def extract_doc_id_from_url(self, url: str) -> Optional[str]:
        """
        从iwiki链接中提取文档ID

        Args:
            url: 文档链接，如：
                - https://iwiki.woa.com/p/4008410284#%E8%8E%B7%E5%8F%96%E6%96%87%E6%A1%A3%E5%86%85%E5%AE%B9
                - https://iwiki.woa.com/p/4007810266

        Returns:
            Optional[str]: 文档ID字符串，提取失败返回None
        """
        try:
            parsed = urlparse(url)
            # 路径形如 /p/4008410284
            parts = parsed.path.strip('/').split('/')
            if len(parts) >= 2 and parts[0] == 'p':
                doc_id = parts[1]
                if doc_id and doc_id.isdigit():
                    return doc_id
            return None
        except Exception:
            return None
        
    def _validate_doc_id_params(self, iwiki_url: str = None, doc_id: str = None) -> str:
        """
        验证并获取文档ID

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :return: 有效的文档ID
        :raises ValueError: 当参数无效时
        """
        if not iwiki_url and not doc_id:
            raise ValueError("必须提供iwiki_url或doc_id")
        
        if doc_id is None:
            doc_id = self.extract_doc_id_from_url(iwiki_url)
            if not doc_id:
                raise ValueError(f"无效的iwiki链接: {iwiki_url}")
        
        return doc_id

    def _clean_text(self, text: str) -> str:
        """
        清理文本，去除标点符号和多余空格

        Args:
            text: 原始文本

        Returns:
            str: 清理后的文本
        """
        return re.sub(r'[^\w\s]', '', text).strip()

    def _match_text(self, text: str, keyword: str, exact: bool = False) -> bool:
        """
        文本匹配函数，支持精确匹配和包含匹配

        Args:
            text: 待匹配的文本
            keyword: 用户输入的关键词
            exact: 是否使用精确匹配，默认为False使用包含匹配

        Returns:
            bool: 是否匹配
        """
        cleaned_text = self._clean_text(text)
        cleaned_keyword = self._clean_text(keyword)
        
        if exact:
            # 精确匹配时，直接比较清理后的文本和关键词
            return cleaned_text == cleaned_keyword
        
        # 包含匹配，检查清理后的文本是否包含清理后的关键词
        return cleaned_keyword in cleaned_text

    def _timestamp_to_str(self, timestamp: int) -> str:
        """
        将毫秒时间戳转换为年月日格式的日期字符串

        Args:
            timestamp: 毫秒时间戳

        Returns:
            str: 格式化的日期字符串，格式为 YYYY-MM-DD
        """
        return datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d')

    def get_doc_body(self, iwiki_url: str, bodymode: str = 'md') -> Optional[str]:
        """
        获取文档正文内容

        :param iwiki_url: iwiki文档链接
        :param bodymode: 返回内容格式，默认 'md'，可选 'view' 等
        :return: 文档正文字符串，失败返回 None
        """
        url = self._build_url(self.DOC_BODY_PATH)
        headers = self._prepare_common_headers()

        doc_id = self.extract_doc_id_from_url(iwiki_url)
        if not doc_id:
            raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")

        params = {
            'id': doc_id,
            'bodymode': bodymode
        }

        try:
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()
            if result.get('msg') == 'ok':
                return result['data']['body']
            else:
                raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未能获取到iwiki文档内容: {result.get('msg')}")
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未能获取到iwiki文档内容: {e}")

    def get_table_body(self, iwiki_url: str = None, doc_id: str = None, view_id: str = None) -> Optional[Dict]:
        """
        获取iwiki表格内容

        Args:
            iwiki_url: iwiki文档链接
            doc_id: 文档ID
            view_id: 视图ID，可选

        Returns:
            Optional[Dict]: 表格内容字典，失败返回 None

        Raises:
            DetailedValueError: 
                - 当iwiki链接无效时
                - 当API调用失败时
                - 当未能获取到表格内容时
        """
        url = self._build_url(self.VIKA_RECORDS_PATH)
        headers = self._prepare_common_headers()
        
        doc_id = self._validate_doc_id_params(iwiki_url, doc_id)
        print(f"doc_id: {doc_id}")
        
        params = {
            'doc_id': doc_id,
            'pageNum': 1,
            'pageSize': 100,
            'viewId': view_id
        }

        try:
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()
            return result
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未能获取到iwiki表格内容: {e}")

    def delete_table_record(self, recordIds: List[str], iwiki_url: str = None, doc_id: str = None) -> dict:
        """
        删除iwiki表格记录
        
        :param recordIds: 要删除的记录ID列表
        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :return: 删除操作结果
        """
        doc_id = self._validate_doc_id_params(iwiki_url, doc_id)
        
        # 使用原始的请求方式，可以请求成功
        url = f"{self.server}/ebus/iwiki/prod/tencent/api/vika/third/records"
        params = {"doc_id": int(doc_id), "record_ids": recordIds}
        
        timestamp = str(int(time.time()))
        nonce = str(random.randint(1000, 9999))
        signature = hashlib.sha256()
        string = timestamp + self.paas_token + nonce + timestamp
        signature.update(string.encode())
        signature = signature.hexdigest().upper()
        
        headers = {
            'x-rio-paasid': self.paas_id,
            'x-rio-nonce': nonce,
            'x-rio-timestamp': timestamp,
            'x-rio-signature': signature
        }
        
        print(f"删除记录 - recordIds: {recordIds}")
        print(f"删除记录 - params: {params}")
        
        try:
            response = self.session.delete(url=url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()
            print(f"删除记录结果: {result}")
            return result
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"删除iwiki表格记录失败: {e}")

    def create_document(self, body: str, title: str, parentid: int, spacekey: str = "ailogs",
                       contenttype: str = "MD", body_mode: str = "", vika_mode: str = "") -> dict:
        """
        创建新的iwiki文档

        :param body: 文档内容
        :param title: 文档标题
        :param spacekey: 空间key
        :param parentid: 父文档ID
        :param contenttype: 内容类型，默认"MD"，设置为"VIKA"可创建多维表格
        :param body_mode: 内容模式，默认空字符串
        :param vika_mode: 多维表格模式，当contenttype="VIKA"时可设置为"excel"
        :return: 包含解析结果的字典
        """
        url = self._build_url(self.DOC_CREATE_PATH)
        headers = self._prepare_common_headers()

        params = {
            "body": body,
            "body_mode": body_mode,
            "contenttype": contenttype,
            "spacekey": spacekey,
            "title": title,
            "parentid": parentid
        }

        # 如果是创建多维表格，添加vika_mode参数
        if contenttype == "VIKA" and vika_mode:
            params["vika_mode"] = vika_mode

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_create_document_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"创建iwiki文档失败: {e}")

    def _parse_create_document_response(self, response: dict) -> dict:
        """
        解析创建文档的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'docid': None,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = '文档创建成功'
            parsed_result['request_id'] = response.get('request_id')

            # 提取docid
            data = response.get('data', {})
            if isinstance(data, dict):
                parsed_result['docid'] = data.get('docid') or data.get('id')
        else:
            parsed_result['message'] = f"文档创建失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def update_document(self, title: str, content: str, iwiki_url: str = None, doc_id: str = None,) -> dict:
        """
        更新iwiki文档内容（覆盖原有内容）。 默认md文档

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :param title: 文档标题
        :param content: 新的内容
        :return: 包含解析结果的字典
        """
        if doc_id is None and iwiki_url is None:
            raise ValueError("doc_id和iwiki_url不能同时为空")

        # doc_id为空，使用iwiki_url提取doc_id
        if doc_id is None:
            doc_id = self.extract_doc_id_from_url(iwiki_url)
            if not doc_id:
                raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")        


        url = self._build_url(self.DOC_UPDATE_PATH)
        headers = self._prepare_common_headers()

        params = {
            "id": int(doc_id),
            "title": title,
            "body": content,
            "version": 0,
            "force": True,
            "body_mode": "md"
        }

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_update_document_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"更新iwiki文档内容失败: {e}")
    
    def _parse_update_document_response(self, response: dict) -> dict:
        """
        解析更新文档内容的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = '文档内容更新成功'
            parsed_result['request_id'] = response.get('request_id')
        else:
            parsed_result['message'] = f"文档内容更新失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def append_document(self, iwiki_url: str, title: str, content_to_append: str) -> dict:
        """
        向现有iwiki文档追加内容

        :param iwiki_url: iwiki文档链接
        :param title: 文档标题
        :param content_to_append: 要追加的内容
        :return: 包含解析结果的字典
        """
        document_id = self.extract_doc_id_from_url(iwiki_url)

        url = self._build_url(self.DOC_APPEND_PATH)
        headers = self._prepare_common_headers()

        params = {
            "id": int(document_id),
            "title": title,
            "after": content_to_append
        }

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_append_document_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"追加iwiki文档内容失败: {e}")

    def _parse_append_document_response(self, response: dict) -> dict:
        """
        解析追加文档内容的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = '文档内容追加成功'
            parsed_result['request_id'] = response.get('request_id')
        else:
            parsed_result['message'] = f"文档内容追加失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def post_vika_records(self, iwiki_url: str = None, doc_id: int = None, field_key: str = "name",
                         fields_list: list[dict] = None, records: list[dict] = None) -> dict:
        """
        向vika表格添加记录

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :param field_key: 字段键名，默认为"name"
        :param fields_list: 字段列表，每个元素为字段字典，会自动封装为records格式
        :param records: 完整的记录列表（如果提供此参数，fields_list将被忽略）
        :return: 包含解析结果的字典
        """
        # 参数验证
        if doc_id is None and iwiki_url is None:
            raise ValueError("doc_id和iwiki_url不能同时为空")

        if fields_list is None and records is None:
            raise ValueError("必须提供 fields_list 或 records 参数之一")

        # doc_id为空，使用iwiki_url提取doc_id
        if doc_id is None:
            doc_id = self.extract_doc_id_from_url(iwiki_url)
            if not doc_id:
                raise_value_error(ErrorCode.INVALID_IWIKI_URL, message=f"无效的iwiki链接: {iwiki_url}")

        # 如果提供了fields_list，自动封装为records格式
        if records is None:
            records = [{"fields": fields} for fields in fields_list]

        url = self._build_url(self.VIKA_RECORDS_PATH)
        headers = self._prepare_common_headers()

        params = {
            "doc_id": int(doc_id),
            "fieldKey": field_key,
            "records": records
        }

        # print(f"请求参数：{params}")

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()
            print(f"post_vika_records == 响应内容：{result}")

            # 解析响应结果
            return self._parse_vika_records_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"添加vika记录失败: {e}")

    def _parse_vika_records_response(self, response: dict) -> dict:
        """
        解析vika记录添加的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = 'vika记录添加成功'
            parsed_result['request_id'] = response.get('request_id')
        else:
            parsed_result['message'] = f"vika记录添加失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def get_vika_fields(self, iwiki_url: str = None, doc_id: int = None, view_id: str = None) -> dict:
        """
        获取多维表格的字段信息

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :param view_id: 视图ID（可选）
        :return: 包含解析结果的字典
        """
        doc_id = self._validate_doc_id_params(iwiki_url, doc_id)

        url = self._build_url(self.VIKA_FIELDS_PATH)
        headers = self._prepare_common_headers()

        params = {
            "doc_id": int(doc_id)
        }

        if view_id:
            params["viewId"] = view_id

        try:
            response = self.session.get(url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_vika_fields_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"获取vika字段失败: {e}")

    def _parse_vika_fields_response(self, response: dict) -> dict:
        """
        解析获取vika字段的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'fields': None,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = '获取vika字段成功'
            parsed_result['request_id'] = response.get('request_id')
            parsed_result['fields'] = response.get('data', {}).get('fields', [])
        else:
            parsed_result['message'] = f"获取vika字段失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def delete_vika_field(self, iwiki_url: str = None, doc_id: int = None, field_id: str = None) -> dict:
        """
        删除多维表格的字段

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :param field_id: 字段ID
        :return: 包含解析结果的字典
        """
        # 参数验证
        if field_id is None:
            raise ValueError("field_id不能为空")

        doc_id = self._validate_doc_id_params(iwiki_url, doc_id)

        try:
            # 使用原始的请求方式，可以请求成功
            url = f"{self.server}/ebus/iwiki/prod/tencent/api/vika/third/fields"
            params = {"doc_id": int(doc_id), "fieldId": field_id}

            timestamp = str(int(time.time()))
            nonce = str(random.randint(1000, 9999))
            signature = hashlib.sha256()
            string = timestamp + self.paas_token + nonce + timestamp
            signature.update(string.encode())
            signature = signature.hexdigest().upper()

            headers = {
                'x-rio-paasid': self.paas_id,
                'x-rio-nonce': nonce,
                'x-rio-timestamp': timestamp,
                'x-rio-signature': signature
            }

            response = self.session.delete(url=url, params=params, headers=headers, timeout=10)
            response.raise_for_status()
            result = response.json()
            print(f"删除字段结果: {result}")
            return result
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"删除vika字段失败: {e}")

    def add_vika_field(self, iwiki_url: str = None, doc_id: int = None, field_type: str = None,
                      field_name: str = None, field_property: dict = None) -> dict:
        """
        添加多维表格的字段

        :param iwiki_url: iwiki文档链接
        :param doc_id: 文档ID
        :param field_type: 字段类型，如 "SingleText", "Text", "MultiSelect"
        :param field_name: 字段名称
        :param field_property: 字段属性配置
        :return: 包含解析结果的字典
        """
        # 参数验证
        if field_type is None:
            raise ValueError("field_type不能为空")

        if field_name is None:
            raise ValueError("field_name不能为空")

        doc_id = self._validate_doc_id_params(iwiki_url, doc_id)

        url = self._build_url(self.VIKA_FIELDS_PATH)
        headers = self._prepare_common_headers()

        params = {
            "doc_id": int(doc_id),
            "type": field_type,
            "name": field_name
        }

        if field_property:
            params["property"] = field_property

        try:
            response = self.session.post(url, headers=headers, json=params, timeout=10)
            response.raise_for_status()
            result = response.json()

            # 解析响应结果
            return self._parse_add_vika_field_response(result)
        except requests.RequestException as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"添加vika字段失败: {e}")

    def _parse_add_vika_field_response(self, response: dict) -> dict:
        """
        解析添加vika字段的API响应

        :param response: API原始响应
        :return: 解析后的结果字典
        """
        parsed_result = {
            'success': False,
            'field_id': None,
            'request_id': None,
            'message': '',
            'raw_response': response
        }

        if response.get('code') == 'Ok' and response.get('msg') == 'ok':
            parsed_result['success'] = True
            parsed_result['message'] = 'vika字段添加成功'
            parsed_result['request_id'] = response.get('request_id')

            # 提取新创建的字段ID
            data = response.get('data', {})
            if isinstance(data, dict):
                parsed_result['field_id'] = data.get('id')
        else:
            parsed_result['message'] = f"vika字段添加失败: {response.get('msg', '未知错误')}"
            parsed_result['request_id'] = response.get('request_id')

        return parsed_result

    def create_vika_table_and_customize_fields(self, title: str, parentid: int,
                                             custom_fields: list[dict] = None) -> dict:
        """
        创建多维表格并自定义字段（删除默认字段，添加自定义字段）

        :param title: 表格标题
        :param parentid: 父文档ID
        :param custom_fields: 自定义字段列表，每个元素包含 type, name, property
        :return: 包含操作结果的字典
        """
        result = {
            'success': False,
            'doc_id': None,
            'created_fields': [],
            'deleted_fields': [],
            'message': '',
            'details': {}
        }

        # 1. 创建多维表格
        create_result = self.create_document(
            body="",
            title=title,
            parentid=parentid,
            contenttype="VIKA",
            vika_mode="excel"
        )

        doc_id = create_result['docid']
        result['doc_id'] = doc_id

        try:
            # 删除默认空行数据
            record_result = self.get_table_body(doc_id=doc_id)
            if record_result:
                print(f"获取到的记录: {record_result}")
                record_ids = [record['recordId'] for record in record_result.get('data', {}).get('records', [])]
                print(f"记录ID列表: {record_ids}")
                if record_ids:
                    self.delete_table_record(doc_id=doc_id, recordIds=record_ids)

            # 2. 获取默认字段
            fields_result = self.get_vika_fields(doc_id=doc_id)
            fields = fields_result['fields']
            print(f"字段信息: {fields}")

            if len(fields) < 2:
                result['message'] = f"默认字段数量不足，期望至少2个，实际{len(fields)}个"
                return result

            # 3. 删除后两个默认字段（第一个字段不能删除）
            fields_to_delete = fields[1:]  # 获取除了第1个字段的其余字段
            print(f"待删除字段: {fields_to_delete}")

            for field in fields_to_delete:
                print(f"删除字段: {field.get('name')}")
                field_id = field.get('id')
                print(f"删除字段ID: {field_id}")
                if field_id:
                    delete_result = self.delete_vika_field(doc_id=doc_id, field_id=field_id)
                    print(f"删除字段结果: {delete_result}")

            print("=== 删除默认字段完成 ===")

            # 4. 添加自定义字段
            if custom_fields:
                for field_config in custom_fields:
                    self.add_vika_field(
                        doc_id=doc_id,
                        field_type=field_config.get('type'),
                        field_name=field_config.get('name'),
                        field_property=field_config.get('property')
                    )

        except Exception as e:
            result['message'] = f"操作过程中发生异常: {str(e)}"
            result['details']['exception'] = str(e)

        return result

    def get_version_calendar(self, version: str, node_names: Optional[Union[str, List[str]]] = None) -> List[Dict[str, str]]:
        """
        查询版本日历信息

        Args:
            version: 版本号，如 '899'
            node_names: 节点名称，可以是字符串或字符串列表，可选。如 '集成测试' 或 ['版本发布 buffer', '实验灰度']。
                       当不传入时，返回该版本的所有节点信息。

        Returns:
            List[Dict[str, str]]: 节点信息列表，每个节点包含：
                - node: 节点名称
                - start_time: 开始时间
                - end_time: 结束时间

        Raises:
            DetailedValueError: 
                - 当版本号为空时
                - 当获取版本日历数据失败时
                - 当未找到版本计划信息时
                - 当版本时间信息不完整时
        """
        # 输入验证
        if not version or not version.strip():
            raise_value_error(ErrorCode.MISSING_REQUIRED_FIELD, message="版本号不能为空")

        # 处理node_names参数：如果是字符串，转换为列表
        if isinstance(node_names, str):
            node_names = [node_names]
        elif node_names is not None and not isinstance(node_names, list):
            raise_value_error(ErrorCode.INVALID_PARAMETER_TYPE, message="node_names参数必须是字符串或字符串列表")

        try:
            # 获取表格数据
            result = self.get_table_body('https://iwiki.woa.com/p/4015162122', view_id='viwgxtqw3mqD2')
            records = result.get('data', {}).get('records', [])
            if not records:
                raise_value_error(ErrorCode.IWIKI_API_ERROR, message="获取版本日历数据失败")
            # 查找版本计划记录 - 使用精确匹配
            version_plan = None
            for record in records:
                title = record['fields'].get('标题', '')
                # 使用版本号精确匹配和"计划"关键词进行匹配
                if title and self._match_text(title, f"{version}【计划】", exact=True):
                    version_plan = record
                    break

            if not version_plan:
                raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未找到版本 {version} 的计划信息")

            # 获取版本的时间范围
            version_start = version_plan['fields'].get('开始时间')
            version_end = version_plan['fields'].get('结束时间')
            if not version_start or not version_end:
                raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"版本 {version} 的时间信息不完整")

            # 查找节点的时间信息
            result_list = []
            if node_names is None:
                # 当node_names为None时，返回所有在版本时间范围内的节点
                for record in records:
                    title = record['fields'].get('标题', '')
                    if title and not self._match_text(title, f"{version}【计划】", exact=True):  # 排除版本计划本身
                        node_start = record['fields'].get('开始时间')
                        node_end = record['fields'].get('结束时间')
                        if node_start and node_end:
                            # 检查节点时间是否在版本时间范围内
                            if version_start <= node_start <= version_end and version_start <= node_end <= version_end:
                                result_list.append({
                                    "node": f"{version}-{title}",
                                    "start_time": self._timestamp_to_str(node_start),
                                    "end_time": self._timestamp_to_str(node_end)
                                })
            else:
                # 当提供了node_names时，只查询这些节点的信息
                if not any(name.strip() for name in node_names):
                    raise_value_error(ErrorCode.MISSING_REQUIRED_FIELD, message="节点名称不能为空")

                for node_name in node_names:
                    if not node_name.strip():
                        continue

                    found = False
                    for record in records:
                        title = record['fields'].get('标题', '')
                        if title and self._match_text(title, node_name):  # 默认使用包含匹配
                            node_start = record['fields'].get('开始时间')
                            node_end = record['fields'].get('结束时间')
                            if node_start and node_end:
                                # 检查节点时间是否在版本时间范围内
                                if version_start <= node_start <= version_end and version_start <= node_end <= version_end:
                                    result_list.append({
                                        "node": f"{version}-{title}",  # 使用匹配到的实际文本
                                        "start_time": self._timestamp_to_str(node_start),
                                        "end_time": self._timestamp_to_str(node_end)
                                    })
                                    found = True
                    if not found:
                        # 当找不到节点时，抛出错误而不是返回"未找到"的结果
                        raise_value_error(
                            ErrorCode.IWIKI_API_ERROR, 
                            message=f"未找到{version}-{node_name}的计划信息"
                        )

            return result_list
        
        except DetailedValueError as e:
            # 如果是 DetailedValueError，直接抛出，无需再包装一次
            raise
        except Exception as e:
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"查询版本日历失败: {str(e)}")

    def get_version_requirements(self, version: str, dev: Optional[str] = None) -> Dict[str, Any]:
        """
        获取版本需求列表

        Args:
            version: 版本号，如 '899'
            dev: 开发人员名称，可选。如果不提供，则返回该版本下所有需求。

        Returns:
            Dict[str, Any]: 包含需求列表和链接的字典：
                - requirements: List[Dict] 需求列表，每个需求包含：
                    - 研发
                    - 需求
                    - 实验
                    - 新用户灰度分支
                    - 旧用户灰度分支
                - requirements_url: str 版本需求表的链接

        Raises:
            DetailedValueError: 
                - 当版本号为空时
                - 当获取需求分组文档失败时
                - 当未找到版本需求表时
                - 当获取需求表内容失败时
        """
        # 输入验证
        if not version:
            raise_value_error(ErrorCode.MISSING_REQUIRED_FIELD, message="版本号不能为空")

        try:
            # 获取需求分组文档内容
            doc_content = self.get_doc_body('https://iwiki.woa.com/p/4015205409')
            if not doc_content:
                raise_value_error(ErrorCode.IWIKI_API_ERROR, message="获取需求分组文档失败",context={"需求分组文档url":"https://iwiki.woa.com/p/4015205409"})
    
            # 提取代码块内容
            requirements_content = TextParseUtils.extract_code_block(doc_content)
            
            # 解析JSON内容，找到对应版本的iwiki_url
            version_url = None
            for line in requirements_content.strip().split('\n'):
                if line.strip():
                    try:
                        req_info = json.loads(line)
                        if req_info.get('version') == version:
                            version_url = req_info.get('iwiki_url')
                            break
                    except json.JSONDecodeError:
                        continue

            if not version_url:
                raise_value_error(ErrorCode.IWIKI_API_ERROR, message=f"未找到版本 {version} 的需求表")

            # 获取需求表内容
            table_content = self.get_table_body(version_url)
            records = table_content.get('data', {}).get('records', [])
            if not records:
                raise_value_error(
                    ErrorCode.IWIKI_API_ERROR,
                    message="获取需求表内容失败",
                    context={"需求表url": version_url}
                )

            # 分类处理需求表记录
            type_a_records = []  # 包含研发、需求、选项的记录
            type_b_records = []  # 包含选项和灰度分支的记录
            
            for record in records:
                fields = record.get('fields', {})
                if '研发' in fields and '需求' in fields and '选项' in fields:
                    type_a_records.append(fields)
                elif '选项' in fields and ('新用户灰度分支' in fields or '老用户灰度分支' in fields):
                    type_b_records.append(fields)

            # 根据dev找到对应的需求，并匹配实验信息
            result_list = []
            for a_record in type_a_records:
                # 如果指定了dev，则只返回该开发人员的需求
                if dev is not None and a_record.get('研发') != dev:
                    continue
                    
                requirement = {
                    '研发': a_record.get('研发', ''),
                    '需求': a_record.get('需求', ''),
                    '实验': a_record.get('选项', [])[0] if a_record.get('选项') else '',
                }
                
                # 查找对应的实验信息
                for b_record in type_b_records:
                    if b_record.get('选项') and requirement['实验'] in b_record['选项']:
                        requirement.update({
                            '新用户灰度分支': b_record.get('新用户灰度分支', ''),
                            '旧用户灰度分支': b_record.get('老用户灰度分支', '')
                        })
                        break
                
                result_list.append(requirement)

            return {
                'requirements': result_list,
                'requirements_url': f"[版本需求表]({version_url})"
            }

        except DetailedValueError as e:
            # 如果是 DetailedValueError，直接抛出，无需再包装一次
            raise
        except Exception as e:
            # 其他异常转换为 DetailedValueError
            raise_value_error(ErrorCode.IWIKI_API_ERROR, message="获取版本需求失败")

if __name__ == "__main__":
    # 使用示例
    paas_id = 'yyb_ai'
    paas_token = '0PUOHJFWRP6NJY8ZCWEVKXY5N7CW5LSN'
    client = IWikiClient(paas_id, paas_token)

    # doc_id = '4015376194'
    # fields_list = [
    #     {'field': 'title', 'value': '测试标题'},
    #     {'field': 'content', 'value': '测试内容'}
    # ]
    # result = client.post_vika_records(doc_id=doc_id, fields_list=fields_list)

    client.update_document(title='900版本测试', content='测试', doc_id='4015239952')
