from tapdsdk.sdk import TapdAPIClient

"""
Type分类: 产品需求、技术需求、BUG
Creator逻辑:
  - 需求: 使用creator，如有父需求则使用父需求的creator
  - BUG: 使用reporter作为creator
推荐方法: get_simplified_ids_info() - 一行代码获取type和creator
"""
class TapdClient:
    def __init__(self, client_id="yyb_tapd", client_secret="31F684F1-6405-28CB-03E2-E6B60B66780B", workspace_id=20422314):
        """
        初始化TAPD客户端

        :param client_id: TAPD客户端ID
        :param client_secret: TAPD客户端密钥
        :param workspace_id: 工作空间ID
        """
        self.sdk = TapdAPIClient(
            client_id=client_id,
            client_secret=client_secret
        )
        self.workspace_id = workspace_id

    def get_stories(self, story_ids, fields=None):
        """
        获取需求信息

        :param story_ids: 需求ID列表或逗号分隔的字符串
        :param fields: 需要返回的字段，默认包含基本字段
        :return: 需求信息列表
        """
        if isinstance(story_ids, list):
            story_ids = ', '.join(story_ids)

        if fields is None:
            fields = 'type, creator, parent_id, custom_field_11'

        params = {
            'workspace_id': self.workspace_id,
            'id': story_ids,
            'fields': fields
        }

        try:
            result = self.sdk.get_stories(params)
            return result
        except Exception as e:
            print(f"获取需求信息失败: {e}")
            raise

    def get_bugs(self, bug_ids, fields=None):
        """
        获取bug信息

        :param bug_ids: bug ID列表或逗号分隔的字符串
        :param fields: 需要返回的字段，默认包含基本字段
        :return: bug信息列表
        """
        if isinstance(bug_ids, list):
            bug_ids = ', '.join(bug_ids)

        if fields is None:
            fields = 'reporter'

        params = {
            'workspace_id': self.workspace_id,
            'id': bug_ids,
            'fields': fields
        }

        try:
            result = self.sdk.get_bugs(params)
            return result
        except Exception as e:
            print(f"获取bug信息失败: {e}")
            raise

    def get_story_fields_label(self):
        """
        获取需求自定义字段信息

        :return: 字段信息
        """
        params = {
            'workspace_id': self.workspace_id
        }

        try:
            result = self.sdk.get_story_fields_lable(params)
            print("获取需求字段信息成功")
            return result
        except Exception as e:
            print(f"获取需求字段信息失败: {e}")
            raise

    def determine_id_type(self, id_value):
        """
        判断ID是需求类型还是bug类型

        :param id_value: 单个ID值
        :return: 'story' 或 'bug' 或 'unknown'
        """
        try:
            # 先尝试作为需求查询
            story_result = self.get_stories([str(id_value)], fields='id')
            if story_result and story_result.get('data') and len(story_result['data']) > 0:
                return 'story'
        except Exception:
            pass

        try:
            # 再尝试作为bug查询
            bug_result = self.get_bugs([str(id_value)], fields='id')
            if bug_result and bug_result.get('data') and len(bug_result['data']) > 0:
                return 'bug'
        except Exception:
            pass

        return 'unknown'

    def get_parent_story_info(self, parent_id, fields=None):
        """
        获取父需求信息

        :param parent_id: 父需求ID
        :param fields: 需要返回的字段
        :return: 父需求信息
        """
        if fields is None:
            fields = 'creator'

        try:
            result = self.get_stories([str(parent_id)], fields=fields)
            if result and result.get('data') and len(result['data']) > 0:
                # 实际数据在Story键下面
                parent_data = result['data'][0]
                return parent_data.get('Story', {})
            return None
        except Exception as e:
            print(f"获取父需求信息失败: {e}")
            return None

    def get_ids_info(self, id_list):
        """
        获取ID列表的详细信息，包括type和creator

        :param id_list: ID列表
        :return: 包含每个ID的type和creator信息的字典列表
        """
        results = []

        for id_value in id_list:
            id_str = str(id_value)
            result_item = {
                'id': id_str,
                'type': 'unknown',
                'creator': None
            }

            # 判断ID类型
            id_type = self.determine_id_type(id_value)

            if id_type == 'story':
                # 处理需求
                story_info = self._process_story_info(id_str)
                if story_info:
                    result_item['type'] = story_info.get('type', '产品需求')
                    result_item['creator'] = story_info.get('creator')
                    # 保留详细信息
                    result_item.update(story_info)

            elif id_type == 'bug':
                # 处理bug
                bug_info = self._process_bug_info(id_str)
                if bug_info:
                    result_item['type'] = 'BUG'
                    result_item['creator'] = bug_info.get('creator')  # bug的creator是reporter
                    # 保留详细信息
                    result_item.update(bug_info)

            results.append(result_item)

        return results

    def _process_story_info(self, story_id):
        """
        处理需求信息，包括父需求处理

        :param story_id: 需求ID
        :return: 处理后的需求信息
        """
        try:
            # 获取需求详细信息
            story_result = self.get_stories([story_id], fields='type, creator, parent_id, custom_field_11')

            if not story_result or not story_result.get('data') or len(story_result['data']) == 0:
                return None

            story_data = story_result['data'][0]
            # 实际数据在Story键下面
            actual_story_data = story_data.get('Story', {})

            original_type = actual_story_data.get('type', '')
            creator = actual_story_data.get('creator', '')
            parent_id = actual_story_data.get('parent_id', '')
            custom_field_11 = actual_story_data.get('custom_field_11', '')  # 需求颗粒度

            # 映射type到标准分类
            if '产品' in original_type or '运营' in original_type:
                mapped_type = '产品需求'
            elif '技术' in original_type:
                mapped_type = '技术需求'
            else:
                mapped_type = '产品需求'  # 默认归类为产品需求

            result = {
                'type': mapped_type,
                'original_type': original_type,
                'custom_field_11': custom_field_11,
                'creator': creator
            }

            # 如果有父需求（parent_id不为空且不为'0'），获取父需求的creator
            if parent_id and parent_id != '0':
                print(f"需求 {story_id} 有父需求 {parent_id}，获取父需求的creator")
                parent_info = self.get_parent_story_info(parent_id, fields='creator')
                if parent_info and parent_info.get('creator'):
                    result['creator'] = parent_info['creator']
                    result['parent_creator'] = parent_info['creator']
                    print(f"从父需求获取到creator: {parent_info['creator']}")

            return result

        except Exception as e:
            print(f"处理需求信息失败 {story_id}: {e}")
            return None

    def _process_bug_info(self, bug_id):
        """
        处理bug信息

        :param bug_id: bug ID
        :return: 处理后的bug信息
        """
        try:
            # 获取bug详细信息
            bug_result = self.get_bugs([bug_id], fields='reporter')

            if not bug_result or not bug_result.get('data') or len(bug_result['data']) == 0:
                return None

            bug_data = bug_result['data'][0]
            # 实际数据在Bug键下面
            actual_bug_data = bug_data.get('Bug', {})

            reporter = actual_bug_data.get('reporter', '')

            return {
                'type': 'BUG',
                'creator': reporter,  # bug的creator使用reporter
                'reporter': reporter
            }

        except Exception as e:
            print(f"处理bug信息失败 {bug_id}: {e}")
            return None

    def get_simplified_ids_info(self, id_list):
        """
        获取ID列表的简化信息，只返回type和creator

        :param id_list: ID列表
        :return: 包含每个ID的type和creator的字典列表
        """
        detailed_results = self.get_ids_info(id_list)
        simplified_results = []

        for item in detailed_results:
            simplified_item = {
                'id': item['id'],
                'type': item['type'],
                'creator': item.get('creator')
            }

            simplified_results.append(simplified_item)

        return simplified_results
    

    def get_simplified_id_info(self, id):
        """
        获取 单个ID 的简化信息，只返回type和creator

        :param id: ID
        :return: 包含每个ID的type和creator的字典列表
        """
        detailed_result = self.get_id_info(id)

        simplified_result = {
            'id': detailed_result['id'],
            'type': detailed_result['type'],
            'creator': detailed_result.get('creator')
        }

        return simplified_result
    
    def get_id_info(self, id):
        """
        获取 单个ID 的详细信息，包括type和creator

        :param id: ID
        :return: 包含每个ID的type和creator信息的字典列表
        """

        id_str = str(id)
        results = {
            'id': id_str,
            'type': 'unknown',
            'creator': None
        }

        # 判断ID类型
        id_type = self.determine_id_type(id)

        if id_type == 'story':
            # 处理需求
            story_info = self._process_story_info(id_str)
            if story_info:
                results['type'] = story_info.get('type', '产品需求')
                results['creator'] = story_info.get('creator')
                # 保留详细信息
                results.update(story_info)

        elif id_type == 'bug':
            # 处理bug
            bug_info = self._process_bug_info(id_str)
            if bug_info:
                results['type'] = 'BUG'
                results['creator'] = bug_info.get('creator')  # bug的creator是reporter
                # 保留详细信息
                results.update(bug_info)

        return results


if __name__ == "__main__":
    """
    TapdClient 使用示例

    功能说明：
    - 自动判断ID类型（产品需求、技术需求、BUG）
    - 自动获取负责人（需求的creator，有父需求则使用父需求creator；BUG的reporter）
    - 支持批量处理混合ID列表
    """

    # 添加项目根目录到Python路径（用于直接运行此文件）
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

    # 创建客户端
    client = TapdClient()

    # 测试ID列表（包含不同类型）
    test_ids = [
        '1020422314123595113',  # 产品需求 (有父需求)
        '1020422314123987100',  # 产品需求 (User Story，有父需求)
        '1020422314124530644',  # 技术需求 (无父需求)
        '1020422314142629686',  # BUG
        '1020422314142635514'   # BUG
    ]

    print("=== TapdClient 使用示例 ===")
    print(f"测试ID列表: {test_ids}")

    # 基本使用 - 获取简化信息（推荐）
    print(f"\n{'='*50}")
    print("基本使用 - 简化信息")
    print(f"{'='*50}")

    results = client.get_simplified_ids_info(test_ids)

    for item in results:
        print(f"ID: {item['id']} | Type: {item['type']} | Creator: {item['creator']}")

    # 详细信息示例
    print(f"\n{'='*50}")
    print("详细信息示例")
    print(f"{'='*50}")

    detailed_results = client.get_ids_info(['1020422314123987100'])  # User Story示例

    for result in detailed_results:
        print(f"ID: {result['id']}")
        print(f"Type: {result['type']}")
        print(f"Creator: {result['creator']}")
        print(f"原始Type: {result.get('original_type')}")
        print(f"需求颗粒度: {result.get('custom_field_11')}")
        if result.get('parent_creator'):
            print(f"父需求Creator: {result.get('parent_creator')}")
            print("从父需求获取了creator")

    # 实用函数示例
    print(f"\n{'='*50}")
    print("实用函数示例")
    print(f"{'='*50}")

    def get_responsible_persons(id_list):
        """便捷函数：获取ID列表的负责人信息"""
        results = client.get_simplified_ids_info(id_list)
        return {item['id']: {'type': item['type'], 'creator': item['creator']} for item in results}

    # 使用便捷函数
    persons = get_responsible_persons(['1020422314123595113', '1020422314142629686'])
    print("负责人信息:")
    for id_val, info in persons.items():
        print(f"  {id_val}: {info['type']} - {info['creator']}")

    # 批量处理统计
    print(f"\n{'='*50}")
    print("批量处理统计")
    print(f"{'='*50}")

    # 按类型统计
    stats = {'产品需求': 0, '技术需求': 0, 'BUG': 0}
    for result in results:
        if result['type'] in stats:
            stats[result['type']] += 1

    print(f"类型统计: {stats}")

    # 按类型分组显示
    by_type = {'产品需求': [], '技术需求': [], 'BUG': []}
    for result in results:
        if result['type'] in by_type:
            by_type[result['type']].append(f"{result['id']} ({result['creator']})")

    for type_name, items in by_type.items():
        if items:
            print(f"\n{type_name}:")
            for item in items:
                print(f"  {item}")
