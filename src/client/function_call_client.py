import time
import requests

# https://iwiki.woa.com/p/4010937341
class HunyuanFunctionCallClient:

    def __init__(self):
        self.results = {}

    def request_hunyuan_funccall(slef, messages, tools):
        try:
            url = 'http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/app_platform/app_create'
            # url = 'http://hunyuanapi.woa.com/openapi/v1/chat/completions'
            headers = {"Content-Type": "application/json",
                       "wsid": "10697",
                   "Authorization": "00ac8819-7488-4487-bfbd-17f4d760aed8"}
            req_body = {
                "query_id": f"query_inttnt_{time.time()}",
                "model": "7B-MoE-FUNCCALL-SFT-32k",
                "stream": False,
                "messages": messages,
                "forward_service":"hyaide-application-10027",
                "tools": tools,
                "tool_choice": "auto",
                "query":"帮忙分析下载过程是否有异常？"
                }
            resp = requests.post(url, headers=headers, json=req_body)
            print(resp.text)

        except Exception:
            print('function call 异常')
            return -1, {}


if __name__ == '__main__':
    tools = [
        {
            "type": "function",
            "function": {
                "name": "call_download_agent",
                "description": "分析下载流程日志，回答下载流程是否存在异常并给出异常原因",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "time": {
                            "time": "时间",
                            "type": "string"
                        }
                    },
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "call_install_agent",
                "description": "分析安装流程日志，回答安装流程是否存在异常并给出异常原因",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "time": {
                            "time": "时间",
                            "type": "string"
                        }
                    },
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "get_current_weather",
                "description": "获取当前地点的天气",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "城市名称"
                        },
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"]
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]

    messages = [
        {
            "role": "user",
            "content" : "帮忙分析下载过程是否有异常"
        },
        {"role": "system", "content": ""},
    ]
    function_call = HunyuanFunctionCallClient()
    function_call.request_hunyuan_funccall(messages=messages, tools=tools)
