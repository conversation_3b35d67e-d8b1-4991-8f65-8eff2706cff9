from src.client.beacon_client import BeaconClient

def main():
    client = BeaconClient()
    # # 测试联网用户数查询功能
    # try:
    #     # 查询2025年5月30日到6月1日的联网用户数
    #     qua_list = [
    #         'TMAF_899_P_7921', 'TMAF_899_P_7851', 'TMAF_899_P_7852',
    #         'TMAF_899_P_7853', 'TMAF_899_P_7924', 'TMAF_899_P_7855',
    #         'TMAF_899_P_7856', 'TMAF_898_P_7857'
    #     ]

    #     # 获取联网用户数数据（嵌套字典格式）
    #     print("=== 联网用户数查询结果 ===")
    #     data = client.query_online_user_count(
    #         start_date="2025-05-30",
    #         end_date="2025-06-01",
    #         qua_list=qua_list
    #     )

    #     print("查询结果数据结构:")
    #     for date in sorted(data.keys(), reverse=True):
    #         print(f"日期: {date}")
    #         for qua, count in data[date].items():
    #             print(f"  QUA: {qua}, 联网用户数: {count}")

    #     # 便捷查询功能
    #     print("=== 便捷查询功能 ===")

    #     # 查询特定日期和QUA的用户数
    #     specific_count = client.get_user_count_by_date_qua(
    #         data, "2025/5/30", "TMAF_899_P_7924"
    #     )
    #     print(f"2025/5/30 TMAF_899_P_7924 的联网用户数: {specific_count}")

    #     # 查询特定日期的所有QUA用户数
    #     date_data = client.get_user_count_by_date(data, "2025/5/30")
    #     print(f"2025/5/30 所有QUA的联网用户数: {date_data}")

    #     # 查询特定QUA在所有日期的用户数
    #     qua_data = client.get_user_count_by_qua(data, "TMAF_899_P_7924")
    #     print(f"TMAF_899_P_7924 在所有日期的联网用户数: {qua_data}")

    # except Exception as e:
    #     print(f"查询失败: {e}")

    # ========== 启动速度分析功能使用示例 ==========
    print("=" * 60)
    print("启动速度分析功能使用示例")
    print("=" * 60)

    try:
        # a. 输入QUA list和时间范围
        print("=== a. 查询指定QUA列表和时间范围的启动速度数据 ===")
        qua_list = ['TMAF_899_P_8547', 'TMAF_899_P_8548', 'TMAF_899_P_8550', 'TMAF_899_P_8549', 'TMAF_899_P_8551', 'TMAF_899_P_8552']
        start_date = "2025-06-06"
        end_date = "2025-06-08"

        print(f"QUA列表: {qua_list}")
        print(f"时间范围: {start_date} ~ {end_date}")

        # 查询启动速度数据
        launch_speed_data = client.query_launch_speed_analytics(
            qua_list=qua_list,
            start_date=start_date,
            end_date=end_date
        )

        print(f"查询完成，获得 {len(launch_speed_data)} 个QUA的数据")

        # 显示数据结构示例
        print("\n=== 数据结构示例 ===")
        for qua, qua_data in list(launch_speed_data.items())[:1]:  # 只显示第一个QUA的数据结构
            print(f"QUA: {qua}")
            for user_type, user_data in qua_data.items():
                print(f"  {user_type}:")
                for launch_type, metrics in user_data.items():
                    count = metrics.get('次数', 0)
                    avg_time = metrics.get('平均值', 0)
                    print(f"    {launch_type}: 次数={count}, 平均值={avg_time}ms")

        # b. 利用a查到的数据，使用便捷函数，根据指定的qua得到新用户和老用户的启动速度
        print("\n=== b. 使用便捷函数获取指定QUA的新用户和老用户启动速度 ===")

        # 指定要查询的QUA
        target_qua = 'TMAF_899_P_8547'
        print(f"目标QUA: {target_qua}")

        # 便捷取数 - 新用户启动速度
        new_user_speed = client.get_new_user_launch_speed(launch_speed_data, target_qua)

        # 便捷取数 - 老用户启动速度
        old_user_speed = client.get_old_user_launch_speed(launch_speed_data, target_qua)

        print(f"\n{target_qua} 新用户启动速度:")
        for launch_type, metrics in new_user_speed.items():
            count = metrics.get('次数', 0)
            avg_time = metrics.get('平均值', 0)
            print(f"  {launch_type}: {avg_time}ms (次数: {count})")

        print(f"\n{target_qua} 老用户启动速度:")
        for launch_type, metrics in old_user_speed.items():
            count = metrics.get('次数', 0)
            avg_time = metrics.get('平均值', 0)
            print(f"  {launch_type}: {avg_time}ms (次数: {count})")

    except Exception as e:
        print(f"查询失败: {e}")


    # # ========== 联网覆盖率功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("联网覆盖率功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询联网用户数据
    #     print("=== 1. 查询联网用户数据 ===")
    #     test_date = "2025-06-08"
    #     test_qua_list = [
    #         'TMAF_899_P_7921', 'TMAF_899_P_7851', 'TMAF_899_P_7852',
    #         'TMAF_899_P_7853', 'TMAF_899_P_7924'
    #     ]

    #     # 1 获取联网用户数据
    #     online_user_data = client.query_online_user_count(
    #         start_date=test_date,
    #         end_date=test_date,
    #         qua_list=test_qua_list
    #     )

    #     # 格式化日期为YYYY/M/D格式
    #     date_parts = test_date.split('-')
    #     formatted_date = f"{date_parts[0]}/{int(date_parts[1])}/{int(date_parts[2])}"

    #     print(f"查询日期: {test_date}")
    #     print(f"查询QUA列表: {test_qua_list}")
    #     print(f"查询结果: {online_user_data.get(formatted_date, {})}")

    #     # 2. 查询总联网用户数
    #     print("\n=== 2. 查询总联网用户数 ===")
    #     total_users = client.query_network_coverage_data(test_date)
    #     print(f"总联网用户数: {total_users}")

    #     # 3. 计算联网覆盖率
    #     print("\n=== 3. 计算联网覆盖率 ===")
    #     coverage_rates = client.calculate_network_coverage_rate(
    #         online_user_data=online_user_data,
    #         date=formatted_date,
    #         total_networking_users=total_users
    #     )

    #     print("联网覆盖率结果:")
    #     for qua, rate in coverage_rates.items():
    #         print(f"  QUA: {qua}, 覆盖率: {rate:.4f} ({rate*100:.2f}%)")

    #     # 4. 使用便捷函数获取特定QUA的覆盖率
    #     print("\n=== 4. 使用便捷函数获取特定QUA的覆盖率 ===")
    #     target_qua = 'TMAF_899_P_7924'
    #     target_rate = client.get_network_coverage_rate_by_qua(coverage_rates, target_qua)
    #     print(f"{target_qua} 的联网覆盖率: {target_rate:.4f} ({target_rate*100:.2f}%)")

    # except Exception as e:
    #     print(f"联网覆盖率查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("联网覆盖率示例结束")
    # print("=" * 60)

    # # ========== 外call下载率功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("外call下载率功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询外call下载数据
    #     print("=== 1. 查询外call下载数据 ===")
    #     test_start_date = "2025-06-06"
    #     test_end_date = "2025-06-08"
    #     # test_start_date = "2025-05-30"
    #     # test_end_date = "2025-06-01"
    #     test_qua_list = [
    #         'TMAF_899_P_8547', 'TMAF_899_P_8548', 'TMAF_899_P_8550',
    #         'TMAF_899_P_8549', 'TMAF_899_P_8551', 'TMAF_899_P_8552'
    #     ]
    #     # qua_exp1 = "TMAF_899_P_7921"
    #     # qua_exp2 = "TMAF_899_P_7851"
    #     # qua_exp3 = "TMAF_899_P_7852"
    #     # qua_control = "TMAF_899_P_7853"
    #     # test_qua_list = [qua_exp1, qua_exp2, qua_exp3, qua_control]

    #     print(f"查询日期范围: {test_start_date} ~ {test_end_date}")
    #     print(f"查询QUA列表: {test_qua_list}")

    #     # 获取外call下载数据
    #     download_data = client.query_external_call_download_data(
    #         start_date=test_start_date,
    #         end_date=test_end_date,
    #         qua_list=test_qua_list
    #     )

    #     print(f"查询完成，获得 {len(download_data)} 个日期的数据")

    #     # 显示部分数据结构
    #     for date in sorted(download_data.keys()):
    #         print(f"\n日期: {date}")
    #         for qua in list(download_data[date].keys())[:2]:  # 只显示前2个QUA
    #             qua_data = download_data[date][qua]
    #             print(f"  QUA: {qua}")
    #             print(f"    外call开始下载率: {qua_data.get('外call开始下载率', '0%')}")
    #             print(f"    外call成功下载率: {qua_data.get('外call成功下载率', '0%')}")
    #             print(f"    外call曝光户数: {qua_data.get('外call曝光户数', 0)}")
    #             print(f"    开始下载户数: {qua_data.get('开始下载户数', 0)}")
    #             print(f"    成功下载户数: {qua_data.get('成功下载户数', 0)}")

    #     # 2. 使用便捷函数获取特定指标
    #     print("\n=== 2. 使用便捷函数获取特定指标 ===")

    #     # 获取特定日期的开始下载率
    #     target_date = "2025/6/6"
    #     if target_date in download_data:
    #         start_rates = client.get_external_call_start_download_rate(download_data, target_date)
    #         print(f"\n{target_date} 所有QUA的外call开始下载率:")
    #         for qua, rate in start_rates.items():
    #             print(f"  {qua}: {rate}")

    #         # 获取特定日期的成功下载率
    #         success_rates = client.get_external_call_success_download_rate(download_data, target_date)
    #         print(f"\n{target_date} 所有QUA的外call成功下载率:")
    #         for qua, rate in success_rates.items():
    #             print(f"  {qua}: {rate}")

    #         # 获取特定QUA的开始下载率
    #         target_qua = 'TMAF_899_P_8547'
    #         if target_qua in download_data.get(target_date, {}):
    #             qua_start_rate = client.get_external_call_start_download_rate(download_data, target_date, target_qua)
    #             qua_success_rate = client.get_external_call_success_download_rate(download_data, target_date, target_qua)
    #             print(f"\n{target_date} {target_qua} 的下载率:")
    #             print(f"  外call开始下载率: {qua_start_rate}")
    #             print(f"  外call成功下载率: {qua_success_rate}")

    #     # 3. 获取特定QUA在所有日期的指标
    #     print("\n=== 3. 获取特定QUA在所有日期的指标 ===")
    #     target_qua = 'TMAF_899_P_8547'
    #     qua_metrics = client.get_external_call_download_metrics_by_qua(download_data, target_qua)
    #     print(f"\n{target_qua} 在所有日期的下载指标:")
    #     for date, metrics in qua_metrics.items():
    #         print(f"  {date}:")
    #         print(f"    外call开始下载率: {metrics.get('外call开始下载率', '0%')}")
    #         print(f"    外call成功下载率: {metrics.get('外call成功下载率', '0%')}")

    # except Exception as e:
    #     print(f"外call下载率查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("外call下载率示例结束")
    # print("=" * 60)

    # # ========== 云游插件拉起成功率功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("云游插件拉起成功率功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询云游插件拉起成功率数据
    #     print("=== 1. 查询云游插件拉起成功率数据 ===")
    #     test_start_date = "2025-06-06"
    #     test_end_date = "2025-06-08"
    #     test_app_version_list = [
    #         '8.9.9_8994130_8549', '8.9.9_8994130_8551', '8.9.9_8994130_8552',
    #         '8.9.9_8994130_8547', '8.9.9_8994130_8548', '8.9.9_8994130_8550'
    #     ]

    #     print(f"查询日期范围: {test_start_date} ~ {test_end_date}")
    #     print(f"查询app_version列表: {test_app_version_list}")

    #     # 获取云游插件拉起成功率数据
    #     plugin_data = client.query_cloud_gaming_plugin_launch_success_rate(
    #         start_date=test_start_date,
    #         end_date=test_end_date,
    #         app_version_list=test_app_version_list
    #     )

    #     print(f"查询完成，获得 {len(plugin_data)} 个日期的数据")

    #     # 显示部分数据结构
    #     for date in sorted(plugin_data.keys()):
    #         print(f"\n日期: {date}")
    #         for app_version in list(plugin_data[date].keys())[:2]:  # 只显示前2个版本
    #             app_data = plugin_data[date][app_version]
    #             print(f"  app_version: {app_version}")
    #             print(f"    云游插件拉起成功率: {app_data.get('云游插件拉起成功率', 0):.4f} ({app_data.get('云游插件拉起成功率', 0)*100:.2f}%)")
    #             print(f"    端内插件点击到拉起率: {app_data.get('端内插件点击到拉起率', 0):.4f} ({app_data.get('端内插件点击到拉起率', 0)*100:.2f}%)")
    #             print(f"    tamst跳转用户数: {app_data.get('tamst跳转用户数', 0)}")
    #             print(f"    openActivity用户数: {app_data.get('openActivity用户数', 0)}")
    #             print(f"    openActivity实际启动用户数: {app_data.get('openActivity实际启动用户数', 0)}")

    #     # 2. 使用便捷函数获取特定指标
    #     print("\n=== 2. 使用便捷函数获取特定指标 ===")

    #     # 获取特定日期的云游插件拉起成功率
    #     target_date = "2025-06-06"
    #     if target_date in plugin_data:
    #         # 获取该日期所有版本的平均成功率
    #         avg_success_rate = client.get_cloud_gaming_plugin_launch_success_rate(plugin_data, target_date)
    #         print(f"\n{target_date} 所有版本的平均云游插件拉起成功率: {avg_success_rate:.4f} ({avg_success_rate*100:.2f}%)")

    #         # 获取特定版本的成功率
    #         target_app_version = '8.9.9_8994130_8547'
    #         if target_app_version in plugin_data.get(target_date, {}):
    #             version_success_rate = client.get_cloud_gaming_plugin_launch_success_rate(plugin_data, target_date, target_app_version)
    #             version_launch_rate = client.get_cloud_gaming_plugin_click_to_launch_rate(plugin_data, target_date, target_app_version)
    #             print(f"\n{target_date} {target_app_version} 的指标:")
    #             print(f"  云游插件拉起成功率: {version_success_rate:.4f} ({version_success_rate*100:.2f}%)")
    #             print(f"  端内插件点击到拉起率: {version_launch_rate:.4f} ({version_launch_rate*100:.2f}%)")

    #     # 3. 获取特定版本在所有日期的指标
    #     print("\n=== 3. 获取特定版本在所有日期的指标 ===")
    #     target_app_version = '8.9.9_8994130_8547'
    #     version_metrics = client.get_cloud_gaming_plugin_metrics_by_app_version(plugin_data, target_app_version)
    #     print(f"\n{target_app_version} 在所有日期的指标:")
    #     for date, metrics in version_metrics.items():
    #         print(f"  {date}:")
    #         print(f"    云游插件拉起成功率: {metrics.get('云游插件拉起成功率', 0):.4f} ({metrics.get('云游插件拉起成功率', 0)*100:.2f}%)")
    #         print(f"    端内插件点击到拉起率: {metrics.get('端内插件点击到拉起率', 0):.4f} ({metrics.get('端内插件点击到拉起率', 0)*100:.2f}%)")

    #     # 4. 获取特定日期的所有版本指标
    #     print("\n=== 4. 获取特定日期的所有版本指标 ===")
    #     target_date = "2025-06-06"
    #     date_metrics = client.get_cloud_gaming_plugin_metrics_by_date(plugin_data, target_date)
    #     print(f"\n{target_date} 所有版本的云游插件拉起成功率:")
    #     for app_version, metrics in date_metrics.items():
    #         success_rate = metrics.get('云游插件拉起成功率', 0)
    #         print(f"  {app_version}: {success_rate:.4f} ({success_rate*100:.2f}%)")

    # except Exception as e:
    #     print(f"云游插件拉起成功率查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("云游插件拉起成功率示例结束")
    # print("=" * 60)

    # # ========== 弹窗成功率功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("弹窗成功率功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询弹窗成功率数据
    #     print("=== 1. 查询弹窗成功率数据 ===")
    #     test_start_date = "2025-06-04"
    #     test_end_date = "2025-06-10"
    #     # test_qua_list = [
    #     #     'TMAF_899_P_8547', 'TMAF_899_P_8548', 'TMAF_899_P_8550',
    #     #     'TMAF_899_P_8549', 'TMAF_899_P_8551', 'TMAF_899_P_8552'
    #     # ]
    #     test_qua_list = ['TMAF_842_F_6869']
    #     test_user_type_list = ['remain', 'silent_back']  

    #     print(f"查询日期范围: {test_start_date} ~ {test_end_date}")
    #     print(f"查询QUA列表: {test_qua_list}")
    #     print(f"查询用户类型: {test_user_type_list}")

    #     # 获取弹窗成功率数据
    #     popup_data = client.query_popup_success_rate(
    #         start_date=test_start_date,
    #         end_date=test_end_date,
    #         qua_list=test_qua_list,
    #         user_type_list=test_user_type_list
    #     )

    #     print(f"查询完成，获得 {len(popup_data)} 个日期的数据")

    #     # 显示部分数据结构
    #     for date in sorted(popup_data.keys()):
    #         print(f"\n日期: {date}")
    #         for qua in list(popup_data[date].keys())[:2]:  # 只显示前2个QUA
    #             qua_data = popup_data[date][qua]
    #             print(f"  QUA: {qua}")
    #             print(f"    弹窗成功率: {qua_data.get('弹窗成功率(%)', 0):.2f}%")
    #             print(f"    弹窗人数: {qua_data.get('弹窗人数', 0)}")
    #             print(f"    曝光人数: {qua_data.get('曝光人数', 0)}")

    #     # 2. 使用便捷函数获取特定指标
    #     print("\n=== 2. 使用便捷函数获取特定指标 ===")

    #     # 获取特定日期的弹窗成功率
    #     target_date = "2025/6/6"
    #     if target_date in popup_data:
    #         # 获取该日期所有QUA的平均成功率
    #         avg_success_rate = client.get_popup_success_rate(popup_data, target_date)
    #         print(f"\n{target_date} 所有QUA的平均弹窗成功率: {avg_success_rate:.2f}%")

    #         # 获取特定QUA的成功率
    #         target_qua = 'TMAF_899_P_8547'
    #         if target_qua in popup_data.get(target_date, {}):
    #             qua_success_rate = client.get_popup_success_rate(popup_data, target_date, target_qua)
    #             qua_exposure_rate = client.get_popup_exposure_rate(popup_data, target_date, target_qua)
    #             print(f"\n{target_date} {target_qua} 的指标:")
    #             print(f"  弹窗成功率: {qua_success_rate:.2f}%")
    #             print(f"  弹窗曝光率: {qua_exposure_rate:.2f}%")

    #         # 获取该日期所有QUA的曝光率
    #         all_exposure_rates = client.get_popup_exposure_rate(popup_data, target_date)
    #         print(f"\n{target_date} 所有QUA的弹窗曝光率:")
    #         for qua, rate in all_exposure_rates.items():
    #             print(f"  {qua}: {rate:.2f}%")

    #     # 3. 获取特定QUA在所有日期的指标
    #     print("\n=== 3. 获取特定QUA在所有日期的指标 ===")
    #     target_qua = 'TMAF_899_P_8547'
    #     qua_metrics = client.get_popup_metrics_by_qua(popup_data, target_qua)
    #     print(f"\n{target_qua} 在所有日期的弹窗指标:")
    #     for date, metrics in qua_metrics.items():
    #         print(f"  {date}:")
    #         print(f"    弹窗成功率: {metrics.get('弹窗成功率(%)', 0):.2f}%")
    #         print(f"    弹窗人数: {metrics.get('弹窗人数', 0)}")
    #         print(f"    曝光人数: {metrics.get('曝光人数', 0)}")

    #     # 4. 获取特定日期的所有QUA指标
    #     print("\n=== 4. 获取特定日期的所有QUA指标 ===")
    #     target_date = "2025/6/6"
    #     date_metrics = client.get_popup_metrics_by_date(popup_data, target_date)
    #     print(f"\n{target_date} 所有QUA的弹窗成功率:")
    #     for qua, metrics in date_metrics.items():
    #         success_rate = metrics.get('弹窗成功率(%)', 0)
    #         print(f"  {qua}: {success_rate:.2f}%")

    # except Exception as e:
    #     print(f"弹窗成功率查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("弹窗成功率示例结束")
    # print("=" * 60)

    # # ========== 下载安装CVR功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("下载安装CVR功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询下载安装CVR数据
    #     print("=== 1. 查询下载安装CVR数据 ===")
    #     test_start_date = "2025-06-06"
    #     test_end_date = "2025-06-08"
    #     test_qua_list = ['TMAF_888_F_5472', 'TMAF_888_F_5739']

    #     print(f"查询日期范围: {test_start_date} ~ {test_end_date}")
    #     print(f"查询QUA列表: {test_qua_list}")

    #     # 获取下载安装CVR数据
    #     cvr_data = client.query_download_install_cvr(
    #         start_date=test_start_date,
    #         end_date=test_end_date,
    #         qua_list=test_qua_list
    #     )

    #     print(f"查询完成，获得 {len(cvr_data)} 个日期的数据")

    #     # 显示部分数据结构
    #     for date in sorted(cvr_data.keys()):
    #         if date == '合计':
    #             continue
    #         print(f"\n日期: {date}")
    #         for qua in list(cvr_data[date].keys())[:2]:  # 只显示前2个QUA
    #             qua_data = cvr_data[date][qua]
    #             print(f"  QUA: {qua}")
    #             print(f"    下载CVR: {qua_data.get('下载CVR', 0):.2f}%")
    #             print(f"    安装CVR: {qua_data.get('安装CVR', 0):.2f}%")
    #             print(f"    下载安装CVR: {qua_data.get('下载安装CVR', 0):.2f}%")
    #             print(f"    开始下载数: {qua_data.get('开始下载数', 0)}")
    #             print(f"    下载成功数: {qua_data.get('下载成功数', 0)}")
    #             print(f"    安装成功数: {qua_data.get('安装成功数', 0)}")

    #     # 2. 使用便捷函数获取特定指标
    #     print("\n=== 2. 使用便捷函数获取特定指标 ===")

    #     # 获取特定日期的CVR指标
    #     target_date = "2025/6/6"
    #     if target_date in cvr_data:
    #         # 获取该日期所有QUA的平均CVR
    #         avg_download_cvr = client.get_download_cvr(cvr_data, target_date)
    #         avg_install_cvr = client.get_install_cvr(cvr_data, target_date)
    #         avg_download_install_cvr = client.get_download_install_cvr(cvr_data, target_date)

    #         print(f"\n{target_date} 所有QUA的平均指标:")
    #         print(f"  平均下载CVR: {avg_download_cvr:.2f}%")
    #         print(f"  平均安装CVR: {avg_install_cvr:.2f}%")
    #         print(f"  平均下载安装CVR: {avg_download_install_cvr:.2f}%")

    #         # 获取特定QUA的CVR指标
    #         target_qua = 'TMAF_888_F_5472'
    #         if target_qua in cvr_data.get(target_date, {}):
    #             qua_download_cvr = client.get_download_cvr(cvr_data, target_date, target_qua)
    #             qua_install_cvr = client.get_install_cvr(cvr_data, target_date, target_qua)
    #             qua_download_install_cvr = client.get_download_install_cvr(cvr_data, target_date, target_qua)

    #             print(f"\n{target_date} {target_qua} 的指标:")
    #             print(f"  下载CVR: {qua_download_cvr:.2f}%")
    #             print(f"  安装CVR: {qua_install_cvr:.2f}%")
    #             print(f"  下载安装CVR: {qua_download_install_cvr:.2f}%")

    #     # 3. 获取特定QUA在所有日期的指标
    #     print("\n=== 3. 获取特定QUA在所有日期的指标 ===")
    #     target_qua = 'TMAF_888_F_5472'
    #     qua_metrics = client.get_download_install_metrics_by_qua(cvr_data, target_qua)
    #     print(f"\n{target_qua} 在所有日期的CVR指标:")
    #     for date, metrics in qua_metrics.items():
    #         if date == '合计':
    #             continue
    #         print(f"  {date}:")
    #         print(f"    下载CVR: {metrics.get('下载CVR', 0):.2f}%")
    #         print(f"    安装CVR: {metrics.get('安装CVR', 0):.2f}%")
    #         print(f"    下载安装CVR: {metrics.get('下载安装CVR', 0):.2f}%")

    #     # 4. 获取特定日期的所有QUA指标
    #     print("\n=== 4. 获取特定日期的所有QUA指标 ===")
    #     target_date = "2025/6/6"
    #     date_metrics = client.get_download_install_metrics_by_date(cvr_data, target_date)
    #     print(f"\n{target_date} 所有QUA的下载安装CVR:")
    #     for qua, metrics in date_metrics.items():
    #         download_install_cvr = metrics.get('下载安装CVR', 0)
    #         print(f"  {qua}: {download_install_cvr:.2f}%")

    # except Exception as e:
    #     print(f"下载安装CVR查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("下载安装CVR示例结束")
    # print("=" * 60)

    # # ========== 广告数据功能使用示例 ==========
    # print("\n" + "=" * 60)
    # print("广告数据功能使用示例")
    # print("=" * 60)

    # try:
    #     # 1. 查询广告数据
    #     print("=== 1. 查询广告数据 ===")
    #     test_start_date = "2025-06-13"
    #     test_end_date = "2025-06-15"
    #     test_version_list = ['TMAF_900_P_9135', 'TMAF_900_P_9136', 'TMAF_900_P_9137', 'TMAF_900_P_9138',
    #                          'TMAF_900_P_9139', 'TMAF_900_P_9140', 'TMAF_900_P_9141', 'TMAF_900_P_9142']  # 可以指定具体版本或使用'all'

    #     print(f"查询日期范围: {test_start_date} ~ {test_end_date}")
    #     print(f"查询版本列表: {test_version_list}")

    #     # 获取广告数据（不按场景分组）
    #     ad_data = client.query_advertisement_data(
    #         start_date=test_start_date,
    #         end_date=test_end_date,
    #         version_list=test_version_list,
    #         scene_split=False
    #     )

    #     print(f"查询完成，获得 {len(ad_data)} 个日期的数据")

    #     # 显示部分数据结构
    #     for date in sorted(ad_data.keys()):
    #         print(f"\n日期: {date}")
    #         for version in list(ad_data[date].keys())[:2]:  # 只显示前2个版本
    #             version_data = ad_data[date][version]
    #             print(f"  版本: {version}")
    #             print(f"    广告曝光: {version_data.get('广告曝光', 0)}")
    #             print(f"    广告点击: {version_data.get('广告点击', 0)}")
    #             print(f"    广告下载: {version_data.get('广告下载', 0)}")
    #             print(f"    广告安装: {version_data.get('广告安装', 0)}")
    #             print(f"    点击曝光率: {version_data.get('点击曝光率', 0):.2f}%")
    #             print(f"    下载点击率: {version_data.get('下载点击率', 0):.2f}%")
    #             print(f"    安装下载率: {version_data.get('安装下载率', 0):.2f}%")

    #     # 2. 使用便捷函数获取特定指标
    #     print("\n=== 2. 使用便捷函数获取特定指标 ===")

    #     # 获取特定日期的广告指标
    #     target_date = "2025/6/6"
    #     if target_date in ad_data:
    #         # 获取该日期的总曝光数
    #         total_exposure = client.get_advertisement_exposure_count(ad_data, target_date)
    #         print(f"\n{target_date} 总广告曝光数: {total_exposure}")

    #         # 获取该日期的转化率
    #         conversion_rates = client.get_advertisement_conversion_rates(ad_data, target_date)
    #         print(f"\n{target_date} 平均转化率:")
    #         print(f"  点击曝光率: {conversion_rates.get('点击曝光率', 0):.2f}%")
    #         print(f"  下载点击率: {conversion_rates.get('下载点击率', 0):.2f}%")
    #         print(f"  安装下载率: {conversion_rates.get('安装下载率', 0):.2f}%")

    #         # 获取特定版本的指标
    #         target_version = 'TMAF_854_P_4915'
    #         if target_version in ad_data.get(target_date, {}):
    #             version_exposure = client.get_advertisement_exposure_count(ad_data, target_date, target_version)
    #             version_rates = client.get_advertisement_conversion_rates(ad_data, target_date, target_version)

    #             print(f"\n{target_date} {target_version} 版本的指标:")
    #             print(f"  广告曝光数: {version_exposure}")
    #             print(f"  点击曝光率: {version_rates.get('点击曝光率', 0):.2f}%")
    #             print(f"  下载点击率: {version_rates.get('下载点击率', 0):.2f}%")
    #             print(f"  安装下载率: {version_rates.get('安装下载率', 0):.2f}%")

    #     # 3. 获取特定版本在所有日期的指标
    #     print("\n=== 3. 获取特定版本在所有日期的指标 ===")
    #     target_version = 'TMAF_854_P_4915'
    #     version_metrics = client.get_advertisement_metrics_by_version(ad_data, target_version)
    #     print(f"\n{target_version} 版本在所有日期的广告指标:")
    #     for date, metrics in version_metrics.items():
    #         print(f"  {date}:")
    #         print(f"    广告曝光: {metrics.get('广告曝光', 0)}")
    #         print(f"    点击曝光率: {metrics.get('点击曝光率', 0):.2f}%")
    #         print(f"    下载点击率: {metrics.get('下载点击率', 0):.2f}%")
    #         print(f"    安装下载率: {metrics.get('安装下载率', 0):.2f}%")

    #     # 4. 获取特定日期的所有版本指标
    #     print("\n=== 4. 获取特定日期的所有版本指标 ===")
    #     target_date = "2025/6/6"
    #     date_metrics = client.get_advertisement_metrics_by_date(ad_data, target_date)
    #     print(f"\n{target_date} 所有版本的广告转化率:")
    #     for version, metrics in date_metrics.items():
    #         click_rate = metrics.get('点击曝光率', 0)
    #         download_rate = metrics.get('下载点击率', 0)
    #         install_rate = metrics.get('安装下载率', 0)
    #         print(f"  {version}: 点击率{click_rate:.2f}%, 下载率{download_rate:.2f}%, 安装率{install_rate:.2f}%")

    # except Exception as e:
    #     print(f"广告数据查询失败: {e}")

    # print("\n" + "=" * 60)
    # print("广告数据示例结束")
    # print("=" * 60)


    # 原始SQL查询示例（保留作为参考）
    # sql = """
    # select
    #     ds, qua, count(distinct guid)
    # from
    #     beacon_olap.dws_ydc_networking_guid_di
    # where
    #     ds >= 20250530
    #     and ds <= 20250601
    #     and qua in ('TMAF_899_P_7921','TMAF_899_P_7851','TMAF_899_P_7852', 'TMAF_899_P_7853', 'TMAF_899_P_7924', 'TMAF_899_P_7855', 'TMAF_899_P_7856', 'TMAF_898_P_7857')
    # group by ds, qua
    # order by qua desc
    #     """
    # response = client.post_model_query(sql=sql, data_source_id=1235751)
    # print("post_model_query状态码:", response.status_code)
    # print("post_model_query响应内容:", response.text)



if __name__ == "__main__":
    main()
