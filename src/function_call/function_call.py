import sys
import os
# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

from src.wecom_bot_svr import ReqMsg
from tokenizers import Tokenizer
from typing import List, Dict, Optional
import json
from datetime import datetime
from src.common.error.detailed_value_error import raise_value_error, ErrorCode
from src.common.logs.logger import app_logger
from src.client.hunyuan_client import model_client_manager
from src.common.logs.loggers.chat_logger import chat_logger
from src.common.logs.loggers.state_logger import state_logger
from src.function_call.prompt.function_call_prompt import FUNCTION_CALL_PROMPT
from src.common.tools.text_parse_utils import TextParseUtils


class FunctionCall:
    def __init__(self):
        pass

    @staticmethod
    def parse_dialog_intent(req_msg: ReqMsg, query: str) -> List[Dict[str, str]]:
        """
        解析对话意图，返回多个问句的意图场景列表
        
        Args:
            req_msg: 请求消息对象，包含chat_id等信息
            query: 用户输入的问题
            
        Returns:
            List[Dict[str, str]]: 包含多个问句及其对应场景的列表
                                  格式: [{"query": "问句", "issue_scene": "场景"}, ...]
        """
        # 获取历史对话记录
        history = chat_logger.get_formatted_chat_history(req_msg.chat_id)
        app_logger.info("-" * 50)
        app_logger.info(f"获取到的历史记录: {history}")
        print("-" * 50)
        print(f"获取到的历史记录: {history}")
        
        # 获取当前状态
        current_state = state_logger.get_current_state_str(req_msg.chat_id)
        app_logger.info("-" * 50)
        app_logger.info(f"获取到的当前状态: {current_state}")
        print("-" * 50)
        print(f"获取到的当前状态: {current_state}")

        # 拼接prompt
        prompt = FUNCTION_CALL_PROMPT.format(
            history=history,
            current_state=current_state,
            query=query
        )
        app_logger.info("-" * 50)
        app_logger.info(f"拼接后的prompt: {prompt}")
        print("-" * 50)
        print(f"拼接后的prompt: {prompt}")

        
        # 调用模型
        model = "DeepSeek-V3-Online"
        result = model_client_manager.get_all_answer(prompt, model)
        
        if not result:
            app_logger.info("模型输出结果为空")
            raise_value_error(ErrorCode.MODEL_STREAM_RESULT_EMPTY, message="模型输出结果为空")

        # 解析模型返回结果
        app_logger.info("-" * 50)
        app_logger.info(f"模型返回结果: {repr(result)}")
        print("-" * 50)
        print(f"模型返回结果: {repr(result)}")
        
        try:
            # 从结果中解析JSON
            data = TextParseUtils.extract_json_from_text(result)
            app_logger.info("-" * 50)
            app_logger.info(f"解析后的JSON数据: {data}")
            print("-" * 50)
            print(f"解析后的JSON数据: {data}")
            
            queries = data.get("queries", [])
            
            app_logger.info("-" * 50)
            app_logger.info(f"提取的queries: {queries}")
            print("-" * 50)
            print(f"提取的queries: {queries}")
            
            app_logger.info("-" * 50)
            app_logger.info("准备返回结果")
            print("-" * 50)
            print("准备返回结果")
            
            # 如果没有解析到queries或为空，则返回原始输入作为单个查询
            if not queries:
                queries = [{"query": query, "issue_scene": "其他"}]
            
            return queries
        except Exception as e:
            app_logger.error(f"JSON解析错误: {str(e)}, 模型返回数据: {result}")
            print(f"JSON解析错误: {str(e)}, 模型返回数据: {result}")
            raise_value_error(ErrorCode.USER_INTENT_SCENE_NOT_FOUND, message="未识别到有效的用户意图场景")




if __name__ == "__main__":
    # 简单测试：传入 query，输出模型回复
    class MockReqMsg:
        def __init__(self, chat_id):
            self.chat_id = chat_id

    test_chat_id = "test_user_with_history"
    test_query = "退出当前功能场景"
    req_msg = MockReqMsg(test_chat_id)
    
    app_logger.info("=" * 60)
    app_logger.info("测试开始")
    app_logger.info("=" * 60)
    app_logger.info(f"测试chat_id: {test_chat_id}")
    app_logger.info(f"测试query: {test_query}")
    app_logger.info("=" * 60)
    
    try:
        queries = FunctionCall.parse_dialog_intent(req_msg, test_query)
        app_logger.info("=" * 60)
        app_logger.info("最终结果:")
        app_logger.info(f"解析结果: {queries}")
        print("=" * 60)
        print("最终结果:")
        print(f"解析结果: {queries}")
        for i, query_info in enumerate(queries):
            print(f"Query {i+1}: {query_info.get('query')} -> {query_info.get('issue_scene')}")
        print("=" * 60)
    except Exception as e:
        app_logger.info("=" * 60)
        app_logger.info("异常信息:")
        app_logger.info(f"模型调用异常: {str(e)}")
        app_logger.info("=" * 60)
