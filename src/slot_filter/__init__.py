"""
SlotFilter - 统一的用户意图解析框架

这个模块提供了一个统一的框架来处理各种用户意图解析需求，
整合了原有的四个独立的用户意图解析器。
"""

from .slot_filter import (
    SlotFilter,
    BaseIntentParser,
    LogAnalyzeIntentParser,
    GrayDataIntentParser,
    VersionMrIntentParser,
    VersionInfoIntentParser,
    slot_filter
)
from src.common.models.slot_filter_result import SlotFilterResult

__all__ = [
    'SlotFilter',
    'BaseIntentParser', 
    'SlotFilterResult',
    'LogAnalyzeIntentParser',
    'GrayDataIntentParser',
    'VersionMrIntentParser',
    'VersionInfoIntentParser',
    'slot_filter'
]

__version__ = '1.0.0'
