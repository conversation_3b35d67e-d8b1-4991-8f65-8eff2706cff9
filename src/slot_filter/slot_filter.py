import json
from typing import Dict, Any, Optional, Tuple, List
from abc import ABC, abstractmethod

from src.common.logs.logger import app_logger
from src.client.hunyuan_client import <PERSON><PERSON>uan<PERSON>lient, model_client_manager
from src.common.error.detailed_value_error import raise_value_error, ErrorCode
from src.common.tools.text_parse_utils import TextParseUtils
from src.common.logs.loggers.chat_logger import chat_logger
from src.common.models.slot_filter_result import SlotFilterResult

# 导入prompt
from .prompt.log_analyze_prompt import LOG_ANALYZE_PROMPT
from .prompt.gray_data_prompt import GRAY_DATA_PROMPT
from .prompt.version_mr_prompt import VERSION_MR_PROMPT
from .prompt.version_info_prompt import VERSION_INFO_PROMPT


class BaseIntentParser(ABC):
    """基础意图解析器抽象类"""
    
    @abstractmethod
    def get_prompt_template(self) -> str:
        """获取提示词模板"""
        pass
    
    @abstractmethod
    def get_model_name(self) -> str:
        """获取使用的模型名称"""
        pass


class SlotFilter:
    """统一的槽位过滤器，整合各种用户意图解析功能"""
    
    def __init__(self):
        self.parsers: Dict[str, BaseIntentParser] = {}
    
    def register_parser(self, parser_name: str, parser: BaseIntentParser):
        """注册意图解析器"""
        self.parsers[parser_name] = parser
    
    def parse_user_input(self, 
                        user_input: str, 
                        parser_name: str,
                        chat_id: str = None) -> SlotFilterResult:
        """
        解析用户输入，提取所需参数
        
        Args:
            user_input: 用户输入的自然语言文本
            parser_name: 解析器名称
            chat_id: 聊天会话ID，可选
            
        Returns:
            SlotFilterResult: 解析结果
        """
        if parser_name not in self.parsers:
            raise_value_error(ErrorCode.PARAMETER_PARSE_FAILED, 
                            message=f"未找到解析器: {parser_name}")
        
        parser = self.parsers[parser_name]
        
        try:
            print(f"slot_filter.parse_user_input 开始执行")
            print("-" * 50)
            app_logger.info(f"开始解析用户输入: {user_input}")
            
            # 构建prompt
            history = chat_logger.get_formatted_chat_history(chat_id)
            prompt_template = parser.get_prompt_template()
            
            prompt = prompt_template.format(
                user_input=user_input,
                history=history if history else "无",
            )
            
            # 调用大模型解析
            model = parser.get_model_name()
            print(f"使用的模型: {model}")
            print(f"构建的prompt: {prompt}")
            print("-" * 50)

            response = model_client_manager.get_all_answer(prompt, model)
            
            if not response:
                app_logger.error("大模型返回结果为空")
                raise_value_error(ErrorCode.MODEL_OVERLOAD, 
                                message="大模型过载，请稍后再试")
            
            # 解析JSON结果
            extracted_params = TextParseUtils.extract_json_from_text(response)
            app_logger.info(f"解析到的参数: {extracted_params}")
            
            # 统一处理输出格式
            is_require_more_info = extracted_params.get("is_require_more_info", "false")
            follow_up_prompt = extracted_params.get("follow_up_prompt", "")
            extracted_fields = extracted_params.get("extracted_fields", {})

            print(f"解析到的参数: {extracted_fields}")
            print("-" * 50)
            
            return SlotFilterResult(
                is_require_more_info=is_require_more_info,
                follow_up_prompt=follow_up_prompt,
                extracted_params=extracted_fields
            )
            
        except Exception as e:
            app_logger.error(f"参数解析失败: {str(e)}")
            raise_value_error(ErrorCode.PARAMETER_PARSE_FAILED, 
                            message=str(e))
     

# 具体的解析器实现
class LogAnalyzeIntentParser(BaseIntentParser):
    """日志分析意图解析器"""
    
    def get_prompt_template(self) -> str:
        return LOG_ANALYZE_PROMPT
    
    def get_model_name(self) -> str:
        return "DeepSeek-V3-Online"


class GrayDataIntentParser(BaseIntentParser):
    """灰度数据分析意图解析器"""
    
    def get_prompt_template(self) -> str:
        return GRAY_DATA_PROMPT
    
    def get_model_name(self) -> str:
        return "DeepSeek-V3-Online"


class VersionMrIntentParser(BaseIntentParser):
    """版本需求列表意图解析器"""
    
    def get_prompt_template(self) -> str:
        return VERSION_MR_PROMPT
    
    def get_model_name(self) -> str:
        return "DeepSeek-V3-Online"


class VersionInfoIntentParser(BaseIntentParser):
    """版本信息查询意图解析器"""
    
    def get_prompt_template(self) -> str:
        return VERSION_INFO_PROMPT
    
    def get_model_name(self) -> str:
        return "DeepSeek-V3-Online"


# 创建全局实例并注册解析器
slot_filter = SlotFilter()
slot_filter.register_parser("log_analyze", LogAnalyzeIntentParser())
slot_filter.register_parser("gray_data", GrayDataIntentParser())
slot_filter.register_parser("version_mr", VersionMrIntentParser())
slot_filter.register_parser("version_info", VersionInfoIntentParser())
