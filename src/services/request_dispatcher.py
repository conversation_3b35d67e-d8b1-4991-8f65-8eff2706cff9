import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

from src.common.logs.logger import app_logger
from src.common.tools.text_parse_utils import TextParseUtils, LogAnalyzeConfig, EvaluateConfig, GrayDataAnalysisConfig
from src.common.tools.file_utils import append_to_file
from src.common.error.detailed_value_error import raise_value_error, ErrorCode

from src.common.logs.loggers.models import Evaluate, Event
from src.common.logs.loggers.evaluate_logger import evaluate_logger
from src.common.logs.loggers.event_logger import event_logger
from src.common.logs.loggers.query_logger import query_logger, QueryStatus
from src.modules.version_tool.gray_data.agent.gray_data_collector import GrayDataAgent
from src.modules.version_tool.version_mr.agent.version_mr_collector import VersionMRAgent
from src.modules.version_tool.version_info.agent.version_info_agent import VersionInfoAgent
from src.modules.other_reply.other_wecom_bot import OtherWecomBot
from src.slot_filter.slot_filter import slot_filter
from src.modules.log_tool.agent.log_analyze_agent import LogAnalyzeAgent
from src.common.models.business_result import BusinessResult
from src.function_call.function_call import FunctionCall
from src.common.logs.loggers.chat_logger import chat_logger
from src.common.logs.loggers.state_logger import state_logger
from src.common.wecom.wecom_bot_tips import WecomBotTips

OUTPUT_DIR = 'output/'

class RequestDispatcher():
    def __init__(self, user_input, user_name, user_cn_name, chat_id):
        self._user_input = user_input
        self._user_name = user_name
        self._user_cn_name = user_cn_name
        self._chat_id = chat_id

    def process_multi_request(self, req_msg, server, ticket_id: str) -> BusinessResult:
        """多问句统一业务编排入口——一次只处理队列中的首条 Query，立即返回结果"""
        try:
            chat_id = req_msg.chat_id
            user_input = req_msg.content

            # 清理已完成/失败的历史记录，避免日志堆积
            query_logger.clear_completed_and_failed(chat_id)

            # 对 WAITING_FOLLOWUP 状态的查询做超时处理，防止长时间挂起
            query_logger.expire_waiting_queries(chat_id, timeout_minutes=5)

            # 定义可识别的跳过/取消关键词
            skip_keywords = {"跳过", "取消", "退出"}

            # 1. 是否存在等待追问的查询？如果有，则把这次输入当作追问回复
            waiting_query = next(
                (q for q in query_logger.list_pending_or_waiting(chat_id)
                 if q.get("status") == QueryStatus.WAITING_FOLLOWUP.value),
                None
            )

            # 如果用户输入的是跳过且当前存在等待追问的查询，则直接终结该查询
            if waiting_query and user_input.strip().lower() in skip_keywords:
                query_logger.update_query_status(chat_id, waiting_query["query_id"], QueryStatus.FAILED, error="user skipped followup")
                # 将 waiting_query 置空，继续后续 PENDING 流程
                waiting_query = None
                # 清空本次输入，避免解析成新的 Query
                user_input = ""

            if waiting_query:
                # 检查追问次数是否超过限制
                if query_logger.check_followup_limit(chat_id, waiting_query["query_id"], max_followup=3):
                    # 追问次数超过限制，标记为失败
                    query_logger.update_query_status(
                        chat_id, 
                        waiting_query["query_id"], 
                        QueryStatus.FAILED, 
                        error="追问次数超过限制(3次)"
                    )
                    # 返回提示信息
                    return BusinessResult.success(
                        data="该问题已追问3次，无法继续处理。您可以重新发送问题或换个方式描述。"
                    )
                
                # 增加追问次数
                query_logger.increment_followup_count(chat_id, waiting_query["query_id"])
                
                result = self._handle_query(waiting_query, user_input, ticket_id)
                return result

            # 2. 没有等待追问 → 解析新输入创建 PENDING 查询
            # 如果用户输入为空，表示当前正在处理PENDING list，无需进行function_call
            if user_input.strip():
                for q in FunctionCall.parse_dialog_intent(req_msg, user_input):
                    query_logger.create_query(chat_id, q.get("query"), q.get("issue_scene"))

            # 3. 取第一条 PENDING 查询并执行
            next_query = next((q for q in query_logger.list_pending_or_waiting(chat_id)
                           if q.get("status") == QueryStatus.PENDING.value), None)

            # 4. 没有 PENDING 查询 → 结束
            if not next_query:
                return BusinessResult.success(data="暂无待处理查询")

            # 切换用户查询状态
            issue_scene = next_query.get("issue_scene")
            if state_logger.get_current_state_str(chat_id) != issue_scene:
                state_logger.update_user_state(chat_id, issue_scene)
                app_logger.info(f'用户切换到新场景：{issue_scene}')

            result = self._handle_query(next_query, next_query.get("query_text"), ticket_id)
            return result

        except Exception as e:
            app_logger.error(f"用户输入处理失败，error_message：{str(e)}")
            return BusinessResult.fail(str(e))

    def process_pending_queries(self, chat_id: str, server, ticket_id: str) -> BusinessResult:
        """专门处理PENDING状态的查询,无需再调用function_call解析用户输入"""
        try:
            # 清理已完成/失败的历史记录，避免日志堆积
            query_logger.clear_completed_and_failed(chat_id)

            # 处理 WAITING_FOLLOWUP 超时
            query_logger.expire_waiting_queries(chat_id, timeout_minutes=10)
            
            # 取第一条 PENDING 查询并执行
            next_query = next((q for q in query_logger.list_pending_or_waiting(chat_id)
                           if q.get("status") == QueryStatus.PENDING.value), None)

            if not next_query:
                return BusinessResult.success(data="暂无待处理查询")

            # 场景切换
            issue_scene = next_query.get("issue_scene")
            if state_logger.get_current_state_str(chat_id) != issue_scene:
                state_logger.update_user_state(chat_id, issue_scene)
                app_logger.info(f'用户切换到新场景：{issue_scene}')

            # 直接调用业务逻辑，不需要req_msg参数
            result = self._handle_query(next_query, next_query.get("query_text"), ticket_id)
            return result

        except Exception as e:
            app_logger.error(f"处理PENDING查询失败：{str(e)}")
            return BusinessResult.fail(str(e))

    def _handle_query(self, query_info: Dict[str, Any], input_text: str, ticket_id: str):
        """根据查询信息执行对应业务逻辑，并维护查询状态"""
        query_id = query_info["query_id"]
        issue_scene = query_info["issue_scene"]

        # 更新当前输入内容供解析器使用
        original_input = self._user_input
        self._user_input = input_text

        # 标记为处理中
        query_logger.update_query_status(self._chat_id, query_id, QueryStatus.PROCESSING)

        try:
            # 执行业务逻辑
            if issue_scene == '日志分析':
                result = self.log_analysis(ticket_id, query_id)
            elif issue_scene == '灰度实验数据分析':
                result = self.gray_data_analysis(ticket_id, query_id)
            elif issue_scene == '版本需求列表':
                result = self.version_mr_collect(query_id)
            elif issue_scene == '版本信息查询':
                result = self.version_info(query_id)
            elif issue_scene == '满意度回访':
                result = self.save_evaluate()
            else:
                # 其他场景
                result = self.other_reply()
            
            setattr(result, "query_text", input_text)
            # 统一处理查询状态更新
            if result.success:
                # 检查当前查询状态，如果已经是WAITING_FOLLOWUP状态，则不更新为COMPLETED
                current_query = query_logger.get_query_info(self._chat_id, query_id)
                if current_query and current_query.get("status") == QueryStatus.WAITING_FOLLOWUP.value:
                    # 已经在业务方法中设置了追问状态，不需要再次更新
                    pass
                else:
                    # 正常完成的查询
                    query_logger.update_query_status(self._chat_id, query_id, QueryStatus.COMPLETED, result=str(result.data))
            else:
                # 处理失败
                query_logger.update_query_status(self._chat_id, query_id, QueryStatus.FAILED, error=result.error)
            
            # 将本次处理的原始 query 文本挂到结果对象上，方便后续生成前缀
            
            return result
            
        except Exception as e:
            # 异常处理
            query_logger.update_query_status(self._chat_id, query_id, QueryStatus.FAILED, error=str(e))
            return BusinessResult.fail(str(e))
        finally:
            # 恢复原始输入，避免影响外部状态
            self._user_input = original_input

    def log_analysis(self, ticket_id, query_id: Optional[str] = None) -> BusinessResult:
        """
        统一的日志分析处理流程
        
        处理流程：
        1. 解析用户输入，提取参数
        2. 判断是否需要追问
        3. 将extracted_params传递给LogAnalyzeAgent处理后续逻辑
        
        Returns:
            BusinessResult: 业务处理结果
        """
        print("=" * 80)
        print("RequestDispatcher.log_analysis 开始执行")
        print("=" * 80)
        
        try:
            print("开始调用 SlotFilter.parse_user_input...")
            slot_filter_result = slot_filter.parse_user_input(self._user_input, "log_analyze", self._chat_id)
            print(f"解析结果: {slot_filter_result}")
            
            # 判断是否需要追问
            if slot_filter_result.is_require_more_info == 'true':
                follow_up_prompt = slot_filter_result.follow_up_prompt
                print(f"需要追问，追问内容: {follow_up_prompt}")
                if query_id:
                    # 更新查询状态为等待追问
                    query_logger.update_query_status(self._chat_id, query_id, QueryStatus.WAITING_FOLLOWUP, followup_prompt=follow_up_prompt)
                return BusinessResult.success(data=follow_up_prompt)
            
            # 将extracted_params传递给LogAnalyzeAgent处理后续逻辑
            analysis_result = LogAnalyzeAgent.process_log_analysis(
                extracted_params=slot_filter_result.extracted_params,
                ticket_id=ticket_id,
                user_name=self._user_name,
                user_cn_name=self._user_cn_name
            )
            
            # 根据分析结果类型返回内容
            if analysis_result.result_type == "error":
                return BusinessResult.fail(analysis_result.error_message)
            
            return BusinessResult.success(data=analysis_result.content)
                
        except Exception as e:
            app_logger.error(f"日志分析处理失败：{e}")
            return BusinessResult.fail(str(e))

    def save_evaluate(self) -> BusinessResult:
        """
        满意度回访处理
        
        Returns:
            BusinessResult: 业务处理结果
        """
        # 解析用户输入
        parse_result = TextParseUtils.parse(self._user_input, EvaluateConfig)
        ticket_id = parse_result.ticket_id
        evaluate = parse_result.evaluate
        is_send_analyze_process = parse_result.is_send_analyze_process
        if is_send_analyze_process == "是":
            is_send_analyze_process = True
        elif is_send_analyze_process == "否": 
            is_send_analyze_process = False
        if evaluate:
            now = datetime.now()
            # 记录事件
            event = Event(
                timestamp=now.strftime("%Y%m%d-%H%M%S"),
                event_type="evaluate",
                en_name=self._user_name,
                cn_name=self._user_cn_name,
                event_params={"ticket_id": ticket_id,
                              "evaluate": evaluate}
            )
            event_logger.write_event(event)
            # 记录评价
            evaluate_data = Evaluate(
                timestamp=now.strftime("%Y%m%d-%H%M%S"),
                en_name=self._user_name,
                cn_name=self._user_cn_name,
                ticket_id=ticket_id,
                evaluate=evaluate
            )
            evaluate_logger.write_evaluate(evaluate_data)
        
        if is_send_analyze_process:
            # 根据工单得到 用户日志的分析过程
            path = "/data/workspace/logassistant-server/alayze_logs"
            all_items = os.listdir(path)
            for item in all_items:
                if ticket_id in item:
                    file_path = path + '/' + item
                    return BusinessResult.success(data=file_path)
        
        return BusinessResult.success(data="谢谢您的评价")

    def version_info(self, query_id: Optional[str] = None) -> BusinessResult:
        """
        统一的版本信息查询处理流程
        
        处理流程：
        1. 解析用户输入，提取参数
        2. 判断是否需要追问
        3. 将extracted_params传递给VersionInfoAgent处理后续逻辑
        
        Returns:
            BusinessResult: 业务处理结果
        """
        print("=" * 80)
        print("RequestDispatcher.version_info 开始执行")
        print("=" * 80)
        
        try:
            # 检查用户输入是否为空
            if not self._user_input.strip():
                return BusinessResult.success(data="看起来您还没有输入内容。您可以告诉我想查询的信息，例如：\n查询898版本的活跃覆盖率\n查询当前版本计划")
            
            print("开始调用 SlotFilter.parse_user_input...")
            slot_filter_result = slot_filter.parse_user_input(self._user_input, "version_info", self._chat_id)
            print(f"解析结果: {slot_filter_result}")
            
            # 判断是否需要追问
            if slot_filter_result.is_require_more_info == 'true':
                follow_up_prompt = slot_filter_result.follow_up_prompt
                print(f"需要追问，追问内容: {follow_up_prompt}")
                if query_id:
                    query_logger.update_query_status(self._chat_id, query_id, QueryStatus.WAITING_FOLLOWUP, followup_prompt=follow_up_prompt)
                return BusinessResult.success(data=follow_up_prompt)
            
            # 将extracted_params传递给VersionInfoAgent处理后续逻辑
            version_agent = VersionInfoAgent()
            agent_result = version_agent.process_version_info(
                extracted_params=slot_filter_result.extracted_params,
                user_input=self._user_input
            )
            
            # 根据结果类型返回内容
            if agent_result.result_type == "error":
                return BusinessResult.fail(agent_result.error_message)
            
            return BusinessResult.success(data=agent_result.content)
                
        except Exception as e:
            app_logger.error(f"版本信息查询处理失败：{e}")
            return BusinessResult.fail(str(e))

    def gray_data_analysis(self, ticket_id: str = None, query_id: Optional[str] = None) -> BusinessResult:
        """
        统一的灰度数据分析处理流程
        
        处理流程：
        1. 解析用户输入，提取参数
        2. 判断是否需要追问
        3. 将extracted_params传递给GrayDataAgent处理后续逻辑
        
        Returns:
            BusinessResult: 业务处理结果
        """
        print("=" * 80)
        print("RequestDispatcher.gray_data_analysis开始执行")
        print("=" * 80)
        
        try:
            print("开始调用 SlotFilter.parse_user_input...")
            slot_filter_result = slot_filter.parse_user_input(user_input=self._user_input, parser_name="gray_data", chat_id=self._chat_id)
            print(f"解析结果: {slot_filter_result}")
            
            # 判断是否需要追问
            if slot_filter_result.is_require_more_info == 'true':
                follow_up_prompt = slot_filter_result.follow_up_prompt
                print(f"需要追问，追问内容: {follow_up_prompt}")
                if query_id:
                    query_logger.update_query_status(self._chat_id, query_id, QueryStatus.WAITING_FOLLOWUP, followup_prompt=follow_up_prompt)
                return BusinessResult.success(data=follow_up_prompt)
            
            # 将extracted_params传递给GrayDataAgent处理后续逻辑
            gray_data_agent = GrayDataAgent()
            agent_result = gray_data_agent.process_gray_data(
                extracted_params=slot_filter_result.extracted_params
            )
            
            # 根据分析结果类型返回内容
            if agent_result.result_type == "error":
                return BusinessResult.fail(agent_result.error_message)
            
            return BusinessResult.success(data=agent_result.content)
                
        except Exception as e:
            app_logger.error(f"灰度数据分析处理失败：{e}")
            return BusinessResult.fail(str(e))

    def version_mr_collect(self, query_id: Optional[str] = None) -> BusinessResult:
        """
        统一的版本需求列表处理流程
        
        处理流程：
        1. 解析用户输入，提取参数
        2. 判断是否需要追问
        3. 将extracted_params传递给VersionMRAgent处理后续逻辑
        
        Returns:
            BusinessResult: 业务处理结果
        """
        print("=" * 80)
        print("RequestDispatcher.version_mr_collect 开始执行")
        print("=" * 80)
        
        try:
            print("开始调用 SlotFilter.parse_user_input...")
            slot_filter_result = slot_filter.parse_user_input(user_input=self._user_input, parser_name="version_mr", chat_id=self._chat_id)
            print(f"解析结果: {slot_filter_result}")
            
            # 判断是否需要追问
            if slot_filter_result.is_require_more_info == 'true':
                follow_up_prompt = slot_filter_result.follow_up_prompt
                print(f"需要追问，追问内容: {follow_up_prompt}")
                if query_id:
                    query_logger.update_query_status(self._chat_id, query_id, QueryStatus.WAITING_FOLLOWUP, followup_prompt=follow_up_prompt)
                return BusinessResult.success(data=follow_up_prompt)
            
            # 将extracted_params传递给VersionMRAgent处理后续逻辑
            version_mr_agent = VersionMRAgent()
            agent_result = version_mr_agent.process_version_mr(
                extracted_params=slot_filter_result.extracted_params
            )
            
            # 根据结果类型返回内容
            if agent_result.result_type == "error":
                return BusinessResult.fail(agent_result.error_message)
            
            return BusinessResult.success(data=agent_result.content)
                
        except Exception as e:
            app_logger.error(f"版本需求列表处理失败：{e}")
            return BusinessResult.fail(str(e))

    def other_reply(self) -> BusinessResult:
        """
        其他场景回复
        
        Returns:
            BusinessResult: 返回带按钮信息的业务结果
        """
        # 用户输入解析
        query = self._user_input  
                
        if not query.strip():
            content = "看起来您还没有输入内容。"
        else:
            try:
                # 创建OtherWecomBot实例并处理查询
                other_bot = OtherWecomBot()
                content = other_bot.generate_reply(query, chat_id=self._chat_id)
            except Exception as e:
                app_logger.error(f"其他场景回复生成失败: {str(e)}")
                return BusinessResult.fail(str(e))
                
        
        # 返回带按钮信息的BusinessResult
        return BusinessResult.success(
            data=content,
            with_buttons=True,
            callback_id='help_menu',
            button_actions=WecomBotTips.help_button_actions()
        )


#---------------------------------旧询问处理模块---------------------------------
    def process_request(self, req_msg, server, ticket_id: str) -> BusinessResult:
        """
        统一的业务编排入口
        负责意图识别、场景分发、业务执行
        
        Args:
            req_msg: 请求消息对象
            server: 企业微信服务器实例
            ticket_id: 工单ID
            
        Returns:
            BusinessResult: 业务处理结果
        """
        try:
            # 1. 意图识别 (使用现有的 function_call)
            intent_result = FunctionCall.parse_dialog_intent(req_msg, req_msg.content)
            function = intent_result["issue_scene"]
            
            # 2. 获取用户当前状态
            current_state = state_logger.get_current_state_str(req_msg.chat_id)
            app_logger.info(f'用户当前状态: {current_state}')
            app_logger.info(f'识别到的场景: {function}')
            
            # 3. 场景切换处理 - 基于状态比对
            if function != current_state:
                # 更新用户状态为新场景
                state_logger.update_user_state(req_msg.chat_id, function)
                app_logger.info(f'用户切换到新场景：{function}')
            else:
                # 状态相同，不需要切换
                app_logger.info(f'用户保持在当前场景：{function}')
            
            # 4. 业务场景分发和执行
            result = self._dispatch_and_execute(function, req_msg, server, ticket_id)
            return result

        except Exception as e:
            app_logger.error(f"业务编排处理失败：{str(e)}")
            return BusinessResult.fail(str(e))
    
    def _dispatch_and_execute(self, function: str, req_msg, server, ticket_id: str) -> BusinessResult:
        """分发到具体的业务处理方法"""
        if function == '满意度回访':
            return self.save_evaluate()
        elif function == '日志分析':
            return self.log_analysis(ticket_id)
        elif function == '灰度实验数据分析':
            return self.gray_data_analysis(ticket_id)
        elif function == '版本需求列表':
            return self.version_mr_collect()
        elif function == '版本信息查询':
            return self.version_info()
        else:
            # other_reply返回BusinessResult，直接返回
            return self.other_reply()
# if __name__ == "__main__":


#     save_prompt = """
# 【满意度回访】
# ### 工单ID
# 20250513%H4928-5R9B
# ### 满意度
# 5星
# ### 是否需要查看分析过程
# 是
#     """
#     wecom_bot = RequestDispatcher(save_prompt, 'xxx', 'xxx')
#     path = wecom_bot.save_evaluate()
#     print(f'path = {path}')


    # print('result_save_path = ', result_save_path)

"""
多问句处理模式使用说明:

## 核心特性
1. 支持识别用户输入中的多个独立问句
2. 串行处理每个问句，确保有序执行
3. 支持追问状态管理，未完成的查询会阻塞后续查询处理
4. 自动状态切换和历史记录管理

## 使用方法
替换现有的 process_request 调用为 process_multi_request:

```python
# 原有方式
result = dispatcher.process_request(req_msg, server, ticket_id)

# 新的多问句方式  
result = dispatcher.process_multi_request(req_msg, server, ticket_id)
```

## 处理流程
1. 检查是否有等待追问回复的查询
   - 如果有，处理追问回复
   - 如果追问完成，自动处理下一个待处理查询
   
2. 如果没有等待中的查询，解析新输入
   - 识别多个问句并创建查询记录
   - 开始处理第一个查询
   
3. 查询处理结果
   - 如果需要追问：设置状态为 WAITING_FOLLOWUP，等待用户回复
   - 如果查询完成：设置状态为 COMPLETED，处理下一个查询
   
## 查询状态
- PENDING: 等待处理
- PROCESSING: 处理中  
- WAITING_FOLLOWUP: 等待追问回复
- COMPLETED: 已完成
- FAILED: 处理失败

## 目录结构
查询状态文件存储在: logs/query/日期/chat_id/query_id.json

## 示例场景
用户输入: "帮我分析一下昨天的日志，另外查询一下898版本的信息"

处理流程:
1. 识别为两个查询：
   - "帮我分析一下昨天的日志" -> 日志分析
   - "查询一下898版本的信息" -> 版本信息查询
   
2. 先处理日志分析查询
   - 如果需要追问（如缺少日志路径），等待用户回复
   - 用户回复后完成日志分析
   
3. 自动处理版本信息查询
   - 如果需要追问（如需要具体信息类型），等待用户回复
   - 完成后返回合并结果

4. 最终返回两个查询的合并结果
"""
