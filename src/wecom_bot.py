import logging
import sys
import os
from datetime import datetime
import datetime
from collections import deque
import random
import string

from src.wecom_bot_svr import WecomBotServer, RspTextMsg, RspMarkdownMsg, ReqMsg
from src.wecom_bot_svr.req_msg import TextReqMsg, AttachmentReqMsg

from src.common.logs.logger import app_logger
from src.client.wecom_client import WecomClient
from src.common.wecom.wecom_bot_tips import WecomBotTips
from src.services.request_dispatcher import RequestDispatcher

from src.common.tools.file_utils import append_to_file
from src.common.error.detailed_value_error import DetailedValueError
from src.common.logs.loggers.chat_logger import chat_logger
from src.common.logs.loggers.state_logger import state_logger
from src.common.models.business_result import BusinessResult
from src.common.models.collected_result import CollectedResult
from src.common.logs.loggers.query_logger import QueryStatus, query_logger
from src.modules.log_tool.agent.log_analyze_agent import LogAnalyzeAgent
from src.common.tools.text_parse_utils import TextParseUtils, LogAnalyzeConfig


# 参考链接：https://developer.work.weixin.qq.com/document/path/99399

# 记录已处理消息
processed_msg_ids = deque(maxlen=100)

def msg_handler(req_msg: ReqMsg, server: WecomBotServer) -> RspMarkdownMsg:
    """
    企业微信机器人消息处理器
    
    功能:
    1. 处理用户发送的文本消息
    2. 支持[日志分析]、[满意度回访]和[版本查询]三种指令
    3. 实现消息去重机制, 防止重复处理
    4. 记录完整的分析过程到日志文件
    5. 返回相应的响应消息给用户
    
    参数:
        req_msg (ReqMsg): 用户请求消息对象, 包含消息内容, 用户信息, 会话ID等
        server (WecomBotServer): 企业微信机器人服务器实例, 用于发送文件和消息
    
    处理流程:
    1. 记录用户输入信息到日志
    2. 生成工单ID用于追踪
    3. 检查消息是否已处理(去重机制)
    4. 解析消息类型和内容
    5. 根据指令类型执行相应业务逻辑
    6. 返回处理结果给用户
    
    支持的指令:
    - [日志分析]: 分析应用日志文件
    - [满意度回访]: 收集用户评价
    - [版本查询]: 查询版本相关信息（活跃覆盖率、版本需求、版本计划等）
    
    返回值:
        RspMarkdownMsg 或 RspTextMsg: 响应消息对象, 包含要发送给用户的内容
    """
    
    ret = RspMarkdownMsg()                          # 创建响应对象，用于返回markdown格式的消息

    # 处理按钮回调
    if req_msg.msg_type == 'attachment' and isinstance(req_msg, AttachmentReqMsg):
        app_logger.info(f'附件消息：{req_msg.callback_id}')
        for action in req_msg.actions:
            if action.name == 'log_analysis':

                ret.content = WecomBotTips.log_tool_help_markdown()
                # 更新用户状态
                state_logger.update_user_state(req_msg.chat_id, "日志分析")
                # 记录日志：按钮回调-日志分析帮助
                chat_logger.log_chat(
                    chat_id=req_msg.chat_id,
                    user_input=f"按钮回调: {action.name}",
                    output=ret.content
                )
                return ret
            elif action.name == 'version_info_query':
                ret.content = WecomBotTips.version_info_help_markdown()
                # 更新用户状态
                state_logger.update_user_state(req_msg.chat_id, "版本信息查询")
                # 记录日志：按钮回调-版本信息帮助
                chat_logger.log_chat(
                    chat_id=req_msg.chat_id,
                    user_input=f"按钮回调: {action.name}",
                    output=ret.content
                )
                return ret
            elif action.name == 'gray_data_analysis':
                ret.content = WecomBotTips.gray_data_help_markdown()
                # 更新用户状态
                state_logger.update_user_state(req_msg.chat_id, "灰度实验数据分析")
                # 记录日志：按钮回调-灰度数据分析帮助
                chat_logger.log_chat(
                    chat_id=req_msg.chat_id,
                    user_input=f"按钮回调: {action.name}",
                    output=ret.content
                )
                return ret
            elif action.name == 'version_mr':
                ret.content = WecomBotTips.version_mr_help_markdown()
                # 更新用户状态
                state_logger.update_user_state(req_msg.chat_id, "版本需求列表")
                # 记录日志：按钮回调-版本需求帮助
                chat_logger.log_chat(
                    chat_id=req_msg.chat_id,
                    user_input=f"按钮回调: {action.name}",
                    output=ret.content
                )
                return ret

    ## 记录用户输入信息 
    app_logger.info(f'用户输入：{req_msg.content}')
    app_logger.info(f'会话id：{req_msg.chat_id}')
    app_logger.info(f'用户id：{req_msg.from_user.en_name}')
    app_logger.info(f'消息ID：{req_msg.msg_id}')
    
    ## 初始化用户状态
    state_logger.init_user_state(req_msg.chat_id)
    
    ## 生成工单ID，用于追踪整个分析过程 
    ticket_id = generate_ticket_id()
    
    ## 将用户输入信息记录到分析过程日志文件 
    append_to_file(ticket_id, f'>>> 用户输入\n{req_msg.content}\n')
    append_to_file(ticket_id, f'>>> 用户id\n{req_msg.from_user.en_name}\n')
    append_to_file(ticket_id, f'>>> 工单id\n{ticket_id}\n')

    ## 消息去重机制：防止企业微信重试导致重复处理 
    msg_id = req_msg.msg_id
    if msg_id in processed_msg_ids:
        app_logger.info(f'企微机器人重试机制。消息正在处理，跳过')
        ret.content = ""
        return ret
    
    ## 将消息ID添加到已处理集合中 
    processed_msg_ids.append(msg_id)
    app_logger.info(f'已处理消息：{processed_msg_ids}')

    ## 处理文本类型消息 
    if req_msg.msg_type == 'text' and isinstance(req_msg, TextReqMsg):


# 【日志分析】
# ### 日志链接
# https://cms.myapp.com/yyb/2025/08/14/1755164828774_d03d4306f72e1abc64474280d5441561.zip
# ### 用户问题
# 弹窗
# ### bug时间
# 2025-08-14 17:20
# ### 是否需要改写prompt
# 否

        if req_msg.content.strip().lower().startswith('【') and server is not None:
            # 走代码解析
            if req_msg.content.strip().lower().startswith('【日志分析】') and server is not None:
                try:
                    # 使用 TextParseUtils.parse 和 LogAnalyzeConfig 解析用户输入
                    log_config = TextParseUtils.parse(
                        text=req_msg.content,
                        config_cls=LogAnalyzeConfig,
                        section_prefix="### "
                    )

                    # 将解析结果转换为 extracted_params 格式
                    extracted_params = {
                        'log_link': log_config.log_link,
                        'query': log_config.query if log_config.query else log_config.scene,  # 优先使用query，如果没有则使用scene
                        'bug_time': log_config.bug_time,
                        'is_rewrite_prompt': 'true' if log_config.is_rewrite_prompt and log_config.is_rewrite_prompt.lower() in ['是', 'true', '1'] else 'false'
                    }

                    print(f"解析出的参数: {extracted_params}")
                    append_to_file(ticket_id, f'>>> 解析出的日志分析参数\n{extracted_params}\n')

                    analysis_result = LogAnalyzeAgent.process_log_analysis(
                        extracted_params=extracted_params,
                        ticket_id=ticket_id,
                        user_name=req_msg.from_user.en_name,
                        user_cn_name=req_msg.from_user.cn_name
                    )

                    # if result_save_path:
                    #     server.send_file(req_msg.chat_id, result_save_path)
                    #     WecomBotTips.send_evaluate_and_save_prompt(chat_id=req_msg.chat_id, ticket_id=ticket_id)
                    #     return RspTextMsg() 
                    # else:
                    #     ret.content = WecomBotTips.help_markdown()
                    #     return ret

                    # 根据分析结果返回内容
                    if analysis_result.result_type == "success":
                        result_save_path = analysis_result.content
                        print(f"日志分析结果保存路径: {result_save_path}")
                        server.send_file(req_msg.chat_id, result_save_path)
                        WecomBotTips.send_evaluate_and_save_prompt(chat_id=req_msg.chat_id, ticket_id=ticket_id)
                        return ret
                    else:
                        ret.content = f"日志分析失败: {analysis_result.error_message}"
                        return ret

                except Exception as e:
                    ret.content = f'日志分析参数解析失败: {e.args[0]}'
                    return ret
            
            elif req_msg.content.strip().lower().startswith('【满意度回访】') and server is not None:
                try:
                    wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
                    analyze_process_path = wecom_bot.save_evaluate()
                    
                    if analyze_process_path:
                        server.send_file(req_msg.chat_id, analyze_process_path)
                        return RspTextMsg()
                    else:
                        ret.content = "谢谢您的评价"
                        return ret
                except DetailedValueError as e:
                    ret.content = f'{e.args[0]}'
                    return ret

            # elif req_msg.content.strip().lower().startswith('【满意度回访】') and server is not None:
            #     try:
            #         wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
            #         analyze_process_path = wecom_bot.save_evaluate()
                    
            #         if analyze_process_path:
            #             server.send_file(req_msg.chat_id, analyze_process_path)
            #             return RspTextMsg()
            #         else:
            #             ret.content = "谢谢您的评价"
            #             return ret
            #     except DetailedValueError as e:
            #         ret.content = f'{e.args[0]}'
            #         return ret
            # elif req_msg.content.strip().lower().startswith('【灰度实验数据分析】') and server is not None:
            #     # 灰度数据分析
            #     WecomBotTips.webhook_send(req_msg.chat_id, "开始处理您的需求，请稍等...")
            #     wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
            #     iwiki_url = wecom_bot.gray_data_analysis()
            #     content = f'已为你生成灰度分析数据，iwiki链接：[{iwiki_url}]({iwiki_url})'
            #     WecomBotTips.webhook_send(req_msg.chat_id, content)
            #     ret.content = ""
            #     return ret
            # elif req_msg.content.strip() == '4' and server is not None:
            #     # 版本需求列表
            #     ret.content = WecomBotTips.version_mr_help_markdown()
            #     return ret
            # elif req_msg.content.strip().lower().startswith('【版本需求列表】') and server is not None:
            #     # 灰度数据分析
            #     WecomBotTips.webhook_send(req_msg.chat_id, "开始处理您的需求，请稍等...")
            #     wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
            #     iwiki_url = wecom_bot.version_mr_collect()
            #     content = f'已为你生成版本需求列表，iwiki链接：[{iwiki_url}]({iwiki_url})'
            #     WecomBotTips.webhook_send(req_msg.chat_id, content)
            #     ret.content = ""
            #     return ret
            # ## 处理【版本查询】指令 ##
            # elif req_msg.content.strip().lower().startswith('【版本查询】') and server is not None:
            #     try:
            #         wecom_bot = WecomBot(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name)
            #         result = wecom_bot.version_info()
                    
            #         if not result or result.strip() == "":
            #             ret.content = "未查询到相关版本信息，请检查输入格式或内容"
            #             return ret
                
            #         # 返回查询结果
            #         WecomBotTips.webhook_send(chat_id=req_msg.chat_id, msg=result)
            #         ret.content = ""    #返回空字符串，如果使用ret回复只能回复一条，当触发超时重试机制会返回"正在快马加鞭处理你的信息",ret消息只能返回一次,重试消息会覆盖正常回复,改用webhook_send发送
            #         return ret
                    
            #     except Exception as e:
            #         # 记录错误信息
            #         error_msg = f'版本查询失败：{str(e)}'
            #         append_to_file(ticket_id, f'>>> 版本查询错误\n{error_msg}\n')
            #         ret.content = f'版本查询失败：{str(e)}'
            #         return ret

        # 委托给业务编排层处理
        try:
            dispatcher = RequestDispatcher(req_msg.content, req_msg.from_user.en_name, req_msg.from_user.cn_name, req_msg.chat_id)
            result = dispatcher.process_multi_request(req_msg, server, ticket_id)
            
            # 根据聊天类型选择不同的处理策略
            if req_msg.chat_type == 'single':
                # 单聊模式：收集所有结果后合并发送
                handle_single_chat_response(result, dispatcher, server, req_msg, ret, ticket_id)
            else:
                # 群聊模式：保持原有的实时发送机制
                WecomBotTips.webhook_send(req_msg.chat_id, "开始处理您的需求，请稍等...")
                handle_group_chat_response(result, dispatcher, server, req_msg, ret, ticket_id)
            
            return ret
            
        except Exception as e:
            app_logger.error(f'业务处理失败：{str(e)}')
            # 返回错误信息
            error_content = f"处理失败：{str(e)}"
            print("=" * 80)
            print(f"消息处理失败，error_content: {error_content}")
            print("=" * 80)
            # 处理出现异常，返回帮助信息
            help_content = WecomBotTips.busy_markdown()
            WecomBotTips.webhook_send(chat_id=req_msg.chat_id, 
                          msg=help_content, 
                          callback_id='help_menu', 
                          actions=WecomBotTips.help_button_actions())
            # 记录日志
            chat_logger.log_chat(
                chat_id=req_msg.chat_id,
                user_input=req_msg.content,
                output=help_content
            )
            ret.content = ""
            return ret

def handle_single_chat_response(result: BusinessResult, dispatcher: RequestDispatcher, server: WecomBotServer, req_msg: ReqMsg, ret: RspMarkdownMsg, ticket_id: str):
    """处理单聊响应：合并多个结果后一次性发送"""
    app_logger.info('单聊模式：开始收集处理结果')
    
    # 收集所有处理结果
    results_to_send = []
    files_to_send = []
    
    # 处理第一个结果，检查是否为追问
    try:
        collected_result = collect_result_for_merge(result)
    except Exception as e:
        app_logger.error(f'collect_result_for_merge 首次处理失败: {str(e)}')
        collected_result = CollectedResult.text_result(
            content=f"处理查询时发生异常：{str(e)}",
            user_input=req_msg.content
        )
    
    # 处理结果后立即检查是否产生了追问状态
    waiting_followup = any(q.get("status") == QueryStatus.WAITING_FOLLOWUP.value for q in query_logger.list_pending_or_waiting(req_msg.chat_id))
    
    if waiting_followup:
        # 这是追问内容，立即单独发送，不收集
        app_logger.info('检测到追问内容，立即单独发送')
        handle_client_response(result, server, req_msg, ret, ticket_id)
        return ret
    else:
        # 正常结果，收集起来
        if collected_result:
            if collected_result.is_file():
                files_to_send.append(collected_result)
            else:
                results_to_send.append(collected_result)
    
    # 处理剩余的待处理查询
    max_iterations = 10
    iteration_count = 0
    
    pending_queries = query_logger.list_queries(req_msg.chat_id, QueryStatus.PENDING.value)
    if pending_queries:
        app_logger.info(f'发现 {len(pending_queries)} 个待处理查询，继续收集结果...')
    
    while iteration_count < max_iterations:
        try:
            next_result = dispatcher.process_pending_queries(req_msg.chat_id, server, ticket_id)
            app_logger.info(f'处理结果: success={next_result.success}, data={next_result.data}')

            # 若无待处理查询则停止
            if next_result.data == "暂无待处理查询":
                app_logger.info('无更多待处理查询，准备发送合并结果')
                break

            # 检查查询结果是否为追问
            waiting_followup = any(q.get("status") == QueryStatus.WAITING_FOLLOWUP.value for q in query_logger.list_pending_or_waiting(req_msg.chat_id))

            if waiting_followup:
                # 这是追问内容，先发送已收集的结果，然后单独发送追问
                app_logger.info('发现追问内容，先发送已收集结果，再单独发送追问')
                send_collected_results(results_to_send, files_to_send, server, req_msg, ticket_id)
                # 单独发送追问内容
                handle_client_response(next_result, server, req_msg, ret, ticket_id)
                return ret
            else:
                # 正常结果，收集起来
                try:
                    collected_result = collect_result_for_merge(next_result)
                    if collected_result:
                        if collected_result.is_file():
                            files_to_send.append(collected_result)
                        else:
                            results_to_send.append(collected_result)
                except Exception as e:
                    app_logger.error(f'collect_result_for_merge 处理失败: {str(e)}')
                    results_to_send.append(CollectedResult.text_result(
                        content=f"处理查询时发生异常：{str(e)}",
                        user_input=req_msg.content
                    ))

        except Exception as loop_e:
            # 捕获循环中任何异常，记录并跳出循环
            app_logger.error(f'循环处理查询时发生异常: {str(loop_e)}')
            results_to_send.append(CollectedResult.text_result(
                content=f"处理查询时发生异常：{str(loop_e)}",
                user_input=req_msg.content
            ))
            break

        iteration_count += 1

        # 防止死循环的安全检查
        if iteration_count >= max_iterations:
            app_logger.error(f'循环次数超过限制({max_iterations})，强制退出防止死循环')
            results_to_send.append(CollectedResult.text_result(
                content="模型过载，已退出当前查询处理。请重新发送消息。",
                user_input=req_msg.content
            ))
            break
    
    # 发送所有收集到的结果
    send_collected_results(results_to_send, files_to_send, server, req_msg, ticket_id)

def handle_group_chat_response(result: BusinessResult, dispatcher: RequestDispatcher, server: WecomBotServer, req_msg: ReqMsg, ret: RspMarkdownMsg, ticket_id: str):
    """处理群聊响应：保持原有的实时发送机制"""
    app_logger.info('群聊模式：保持实时发送机制')
    
    # 发送第一个结果给用户
    handle_client_response(result, server, req_msg, ret, ticket_id)

    # 如果当前结果需要追问，则结束等待用户回复
    waiting_followup = any(q.get("status") == QueryStatus.WAITING_FOLLOWUP.value for q in query_logger.list_pending_or_waiting(req_msg.chat_id))
    if waiting_followup:
        return ret  # 已通过 webhook 发送追问内容

    # 如果无WAITING_FOLLOWUP状态的查询，则继续处理剩余 PENDING 状态的查询
    max_iterations = 10  # 最大循环次数，防止死循环
    iteration_count = 0
    
    # 检查是否有待处理的查询，如果有则提示用户
    pending_queries = query_logger.list_queries(req_msg.chat_id, QueryStatus.PENDING.value)
    if pending_queries:
        app_logger.info(f'发现 {len(pending_queries)} 个待处理查询，开始处理...')
    
    while iteration_count < max_iterations:
        # 处理下一个待处理查询
        next_result = dispatcher.process_pending_queries(req_msg.chat_id, server, ticket_id)
        app_logger.info(f'处理结果: success={next_result.success}, data={next_result.data}')
        
        # 若无待处理查询则停止
        if next_result.data == "暂无待处理查询":
            app_logger.info('无更多待处理查询，退出循环')
            break
        
        # 发送查询结果给用户
        handle_client_response(next_result, server, req_msg, ret, ticket_id)
        app_logger.info('已处理查询结果')
        
        # 如果有需要追问的查询，则停止继续处理
        if any(q.get("status") == QueryStatus.WAITING_FOLLOWUP.value for q in query_logger.list_pending_or_waiting(req_msg.chat_id)):
            app_logger.info('发现需要追问的查询，退出循环')
            break
        
        iteration_count += 1
        
        # 防止死循环的安全检查                                                                                                                  
        if iteration_count >= max_iterations:
            app_logger.error(f'循环次数超过限制({max_iterations})，强制退出防止死循环')
            WecomBotTips.webhook_send(req_msg.chat_id, "模型过载，已退出当前查询处理。请重新发送消息。")
            break

def collect_result_for_merge(result: BusinessResult) -> CollectedResult:
    """收集结果用于合并发送"""
    # 获取子问题文本，如果没有则使用默认提示
    user_input = result.query_text or "❔"
    
    if not result.success:
        return CollectedResult.text_result(
            content=f"处理失败：{result.error}",
            user_input=user_input
        )
    
    data = result.data
    
    # 判断返回类型
    if isinstance(data, str) and os.path.exists(data):
        # 文件类型
        return CollectedResult.file_result(
            file_path=data,
            user_input=user_input
        )
    else:
        # 文本类型
        return CollectedResult.text_result(
            content=data,
            user_input=user_input,
            with_buttons=result.with_buttons,
            button_actions=result.button_actions if result.with_buttons else None,
            callback_id=result.callback_id if result.with_buttons else None
        )

def send_collected_results(results_to_send: list, files_to_send: list, server: WecomBotServer, req_msg: ReqMsg, ticket_id: str):
    """发送收集到的所有结果"""
    app_logger.info(f'准备发送合并结果：文本消息{len(results_to_send)}条，文件{len(files_to_send)}个')
    
    # 先发送文件
    for file_result in files_to_send:
        app_logger.info(f'发送文件: {file_result.file_path}')
        server.send_file(req_msg.chat_id, file_result.file_path)
        # 记录日志
        chat_logger.log_chat(
            chat_id=req_msg.chat_id,
            user_input=file_result.user_input,
            output=f"已发送文件: {file_result.file_path}"
        )
    
    # 如果有文件发送，则发送满意度回访提示
    if files_to_send:
        WecomBotTips.send_evaluate_and_save_prompt(chat_id=req_msg.chat_id, ticket_id=ticket_id)
    
    # 合并所有文本消息
    if results_to_send:
        # 分离有按钮和无按钮的消息
        button_messages = [r for r in results_to_send if r.with_buttons]
        text_messages = [r for r in results_to_send if not r.with_buttons]
        
        # 先发送合并的文本消息（如果有多条无按钮消息）
        if len(text_messages) > 1:
            # 多查询结果需要添加前缀并用分割线连接
            prefixed_messages = []
            for r in text_messages:
                prefixed_content = f"【关于「{r.user_input}」的查询结果】\n \n{r.content}"
                prefixed_messages.append(prefixed_content)
            merged_content = "\n\n--------查询结果分割线--------\n\n".join(prefixed_messages)
            app_logger.info(f'发送合并文本消息，包含{len(text_messages)}条内容')
            WecomBotTips.webhook_send(chat_id=req_msg.chat_id, msg=merged_content)
            
            # 记录合并日志
            combined_input = " | ".join(set([r.user_input for r in text_messages]))
            chat_logger.log_chat(
                chat_id=req_msg.chat_id,
                user_input=combined_input,
                output=merged_content
            )
        elif len(text_messages) == 1:
            # 只有一条文本消息，直接发送，不添加前缀
            result = text_messages[0]
            app_logger.info(f'发送单条文本消息: {result.content}')
            WecomBotTips.webhook_send(chat_id=req_msg.chat_id, msg=result.content)
            chat_logger.log_chat(
                chat_id=req_msg.chat_id,
                user_input=result.user_input,
                output=result.content
            )
        
        # 再发送带按钮的消息（每条单独发送，因为按钮不能合并）
        for result in button_messages:
            app_logger.info(f'发送带按钮的消息: callback_id={result.callback_id}')
            WecomBotTips.webhook_send(
                chat_id=req_msg.chat_id, 
                msg=result.content,
                callback_id=result.callback_id,
                actions=result.button_actions
            )
            chat_logger.log_chat(
                chat_id=req_msg.chat_id,
                user_input=result.user_input,
                output=result.content
            )

def handle_client_response(result: BusinessResult, server: WecomBotServer, req_msg: ReqMsg, ret: RspMarkdownMsg, ticket_id: str) -> RspMarkdownMsg:
    """统一的响应处理"""
    app_logger.info(f'handle_client_response: success={result.success}, data_type={type(result.data)}, data={result.data}')
    
    if not result.success:
        # 处理出现异常，返回帮助信息
        error_content = f"处理失败：{result.error}"
        print("=" * 80)
        print(f"消息处理失败，error_content: {error_content}")
        print("=" * 80)
        help_content = WecomBotTips.busy_markdown()
        WecomBotTips.webhook_send(chat_id=req_msg.chat_id, 
                          msg=help_content, 
                          callback_id='help_menu', 
                          actions=WecomBotTips.help_button_actions())
        # 记录日志：意图识别失败
        chat_logger.log_chat(
                chat_id=req_msg.chat_id,
                user_input=req_msg.content,
                output=help_content
            )
        ret.content = ""
        return ret

    # 成功处理
    data = result.data
    app_logger.info(f'处理成功，开始发送响应: data={data}')
    
    # 判断返回类型并处理
    if isinstance(data, str) and os.path.exists(data):
        # 日志分析的文件路径，发送文件
        app_logger.info(f'发送文件: {data}')
        server.send_file(req_msg.chat_id, data)
        WecomBotTips.send_evaluate_and_save_prompt(chat_id=req_msg.chat_id, ticket_id=ticket_id)
        chat_logger.log_chat(
            chat_id=req_msg.chat_id,
            user_input=req_msg.content,
            output=f"已发送文件: {data}"
        )
        ret.content = ""
        return ret
    else:
        # 文本内容，发送消息
        app_logger.info(f'发送文本消息: with_buttons={result.with_buttons}, data={data}')
        if result.with_buttons and result.button_actions:
            # 需要添加按钮的情况
            app_logger.info(f'发送带按钮的消息: callback_id={result.callback_id}')
            WecomBotTips.webhook_send(
                chat_id=req_msg.chat_id, 
                msg=data,
                callback_id=result.callback_id,
                actions=result.button_actions
            )
        else:
            # 普通文本消息
            app_logger.info(f'发送普通文本消息: {data}')
            WecomBotTips.webhook_send(chat_id=req_msg.chat_id, msg=data)
        
        app_logger.info(f'消息发送完成，记录日志')
        chat_logger.log_chat(
            chat_id=req_msg.chat_id,
            user_input=req_msg.content,
            output=data
        )
        ret.content = ""
        return ret


# 事件消息
# 参考链接：https://developer.work.weixin.qq.com/document/path/91881
def event_handler(req_msg):
    app_logger.info(f'事件消息: {req_msg.event_type}')
    ret = RspMarkdownMsg()
    if req_msg.event_type == 'add_to_chat':  # 入群事件处理
        print('add_to_chat')
        app_logger.info(f'机器人加入群聊：{req_msg.chat_id}')
        help_content = WecomBotTips.help_markdown()
        WecomBotTips.webhook_send(chat_id=req_msg.chat_id, 
                      msg=help_content, 
                      callback_id='help_menu', 
                      actions=WecomBotTips.help_button_actions())
        ret.content = ""
        # ret.content = f'msg_type: {req_msg.msg_type}\n群会话ID: {req_msg.chat_id}\n查询用法请回复: help'
    elif req_msg.event_type == 'delete_from_chat':  # 出群事件处理 待开发
        print('delete_from_chat')
        # ret.content = WecomBotTips.delete_markdown()
    elif req_msg.event_type == 'enter_chat':  # 进入会话事件处理 待开发
        print('enter_chat')
        app_logger.info(f'用户id：{req_msg.from_user.en_name}, action: enter_chat')
        # ret.content = WecomBotTips.help_markdown()
    return ret


def generate_ticket_id():
    date_str = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    random_str = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
    ticket_id = f"{date_str}-{random_str}"
    return ticket_id
