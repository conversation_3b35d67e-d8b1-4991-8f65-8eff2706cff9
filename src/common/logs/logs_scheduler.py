"""统一的日志调度器，使用 APScheduler 定时清理聊天历史和用户状态。"""

from datetime import datetime

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger

from src.common.logs.loggers.chat_logger import chat_logger
from src.common.logs.loggers.state_logger import state_logger

try:
    from src.common.logs.logger import app_logger
except Exception:
    # 如果日志模块不可用，降级到 print
    import logging

    app_logger = logging.getLogger(__name__)
    if not app_logger.handlers:
        logging.basicConfig(level=logging.INFO)


class LogsScheduler:
    """统一管理日志清理任务。"""

    def __init__(self):
        self.scheduler = BackgroundScheduler(daemon=True)
        self._register_jobs()

    # ------------------------------------------------------------------
    # internal helpers
    # ------------------------------------------------------------------

    def _register_jobs(self):
        """注册所有日志清理任务。"""
        # Chat history – 每 10 分钟滚动清理一次，保留最近 10 分钟内的记录
        self.scheduler.add_job(
            func=lambda: chat_logger.purge_old_records(600),
            trigger=IntervalTrigger(seconds=600),
            id="chat_history_purge",
            name="清理聊天历史 (10min)",
            coalesce=True,
            max_instances=1,
            replace_existing=True,
        )

        # State – 每天 0/6/12/18 点各清理一次
        self.scheduler.add_job(
            func=state_logger._cleanup_old_states,  # pylint: disable=protected-access
            trigger=CronTrigger(hour="0,6,12,18", minute=0),
            id="state_cleanup",
            name="清理用户状态 (0,6,12,18)",
            coalesce=True,
            max_instances=1,
            replace_existing=True,
        )

    # ------------------------------------------------------------------
    # public api
    # ------------------------------------------------------------------

    def start(self):
        if not self.scheduler.running:
            self.scheduler.start()
            app_logger.info(f"日志调度器已启动: {datetime.now().isoformat()}")

    def shutdown(self, wait: bool = True):
        if self.scheduler.running:
            app_logger.info("日志调度器正在关闭...")
            self.scheduler.shutdown(wait=wait)


# 全局调度器实例，模块导入时即启动
logs_scheduler = LogsScheduler()
logs_scheduler.start() 