import os
import json
from datetime import datetime
from typing import Optional, List

from src.common.logs.loggers.base_logger import BaseLogger


class ChatLogger(BaseLogger):
    """
    聊天日志记录器
    按日期分文件夹，按chat_id生成json文件记录用户交互
    """
    
    def __init__(self, base_dir: str = "logs/history"):
        """
        初始化日志记录器
        
        Args:
            base_dir: 日志存储的基础目录
        """
        self.base_dir = base_dir
        os.makedirs(base_dir, exist_ok=True)
    
    def _get_date_folder(self) -> str:
        """获取当前日期的文件夹路径"""
        today = datetime.now().strftime("%Y-%m-%d")
        date_folder = os.path.join(self.base_dir, today)
        os.makedirs(date_folder, exist_ok=True)
        return date_folder
    
    def _get_chat_file_path(self, chat_id: str) -> str:
        """获取聊天记录文件路径"""
        date_folder = self._get_date_folder()
        return os.path.join(date_folder, f"{chat_id}.json")
    
    def log_chat(self, chat_id: str, user_input: str, output: str, dt: Optional[datetime] = None):
        """
        记录聊天交互
        
        Args:
            chat_id: 聊天会话ID
            user_input: 用户输入内容
            output: 机器人输出内容
            dt: 时间戳（可选）
        """
        chat_file = self._get_chat_file_path(chat_id)
        
        # 读取现有记录
        records = []
        if os.path.exists(chat_file):
            try:
                with open(chat_file, 'r', encoding='utf-8') as f:
                    records = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                records = []
        
        # 创建新的交互记录
        interaction = {
            "timestamp": (dt or datetime.now()).isoformat(),
            "user_input": user_input,
            "output": output
        }
        
        records.append(interaction)
        
        # 写入文件
        with open(chat_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
    
    def get_chat_history(self, chat_id: str, date_str: Optional[str] = None) -> List[dict]:
        """
        获取指定聊天会话的历史记录
        
        Args:
            chat_id: 聊天会话ID
            date_str: 日期字符串（格式：YYYY-MM-DD），如果为None则使用今天
            
        Returns:
            聊天记录列表
        """
        if date_str is None:
            date_str = datetime.now().strftime("%Y-%m-%d")
        
        chat_file = os.path.join(self.base_dir, date_str, f"{chat_id}.json")
        
        if os.path.exists(chat_file):
            try:
                with open(chat_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                return []
        return []

    def get_formatted_chat_history(self, chat_id: str, date_str: Optional[str] = None, max_records: int = 10) -> str:
        """
        获取格式化的聊天历史记录，返回最新的N条记录
        
        Args:
            chat_id: 聊天会话ID
            date_str: 日期字符串（格式：YYYY-MM-DD），如果为None则使用今天
            max_records: 最大记录数，默认10条
            
        Returns:
            str: 格式化的历史对话记录
        """
        try:
            # 获取历史记录
            history_records = self.get_chat_history(chat_id, date_str)
            
            if not history_records:
                return "none"
            
            # 取最新的N条记录
            recent_records = history_records[-max_records:] if len(history_records) > max_records else history_records
            
            # 格式化历史记录
            formatted_history = []
            for record in recent_records:
                user_input = record.get("user_input", "")
                output = record.get("output", "")
                if user_input and output:
                    formatted_history.append(f"用户: {user_input}")
                    formatted_history.append(f"助手: {output}")
            
            return "\n".join(formatted_history)
            
        except Exception as e:
            from src.common.logs.logger import app_logger
            app_logger.error(f"获取聊天历史记录失败: {str(e)}")
            return "none"

    def clear_chat_history(self, chat_id: str, date_str: Optional[str] = None):
        """
        清理指定聊天会话的历史记录
        
        Args:
            chat_id: 聊天会话ID
            date_str: 日期字符串（格式：YYYY-MM-DD），如果为None则使用今天
        """
        try:
            if date_str is None:
                date_str = datetime.now().strftime("%Y-%m-%d")
            
            chat_file = os.path.join(self.base_dir, date_str, f"{chat_id}.json")
            
            # 如果文件存在，清空文件内容（保留文件）
            if os.path.exists(chat_file):
                # 写入空的JSON数组，保持文件结构
                with open(chat_file, 'w', encoding='utf-8') as f:
                    json.dump([], f, ensure_ascii=False, indent=2)
                
                from src.common.logs.logger import app_logger
                app_logger.info(f"已清理聊天历史记录: {chat_file}")
            else:
                from src.common.logs.logger import app_logger
                app_logger.info(f"聊天历史记录文件不存在: {chat_file}")
                
        except Exception as e:
            from src.common.logs.logger import app_logger
            app_logger.error(f"清理聊天历史记录失败: {str(e)}")

    def purge_old_records(self, retention_seconds: int = 600):
        """遍历 logs/history 下所有聊天文件，只保留距现在 retention_seconds 内的记录"""
        now_ts = datetime.now().timestamp()
        for date_dir in os.listdir(self.base_dir):
            date_path = os.path.join(self.base_dir, date_dir)
            if not os.path.isdir(date_path):
                continue
            for fname in os.listdir(date_path):
                if not fname.endswith('.json'):
                    continue
                fpath = os.path.join(date_path, fname)
                try:
                    with open(fpath, 'r', encoding='utf-8') as f:
                        records = json.load(f)
                except (json.JSONDecodeError, FileNotFoundError):
                    records = []
                # 过滤记录
                kept = []
                for rec in records:
                    ts_str = rec.get('timestamp')
                    try:
                        ts = datetime.fromisoformat(ts_str).timestamp()
                        if now_ts - ts <= retention_seconds:
                            kept.append(rec)
                    except Exception:
                        # 保守起见，保留无法解析的记录
                        kept.append(rec)
                if kept:
                    with open(fpath, 'w', encoding='utf-8') as f:
                        json.dump(kept, f, ensure_ascii=False, indent=2)
                else:
                    # 若全部过期，删除文件
                    os.remove(fpath)
            # 删除空日期目录
            if not os.listdir(date_path):
                os.rmdir(date_path)


# 全局实例
chat_logger = ChatLogger()


# ---------------- 定时清理任务由 APScheduler 统一管理 ----------------
# 迁移至 src/common/logs/logs_scheduler.py 