import os
import json
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Union
from dataclasses import dataclass, asdict
from enum import Enum
from src.common.logs.loggers.base_logger import BaseLogger

# 日期格式
DATE_FMT = "%Y-%m-%d"


class QueryStatus(Enum):
    """查询状态枚举"""
    PENDING = "pending"          # 等待处理
    PROCESSING = "processing"    # 处理中
    WAITING_FOLLOWUP = "waiting_followup"  # 等待追问回复
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 处理失败

# 数据模型
@dataclass
class QueryRecord:
    query_id: str
    chat_id: str
    query_text: str
    issue_scene: str
    status: str
    created_time: str
    updated_time: str
    followup_count: int = 0  # 追问次数，默认为0
    followup_prompt: Optional[str] = None
    result: Optional[str] = None
    error: Optional[str] = None

# query日志工具
class QueryLogger(BaseLogger):
    """
    查询状态记录器
    按日期分文件夹，按chat_id分子文件夹，按query_id生成json文件记录查询状态
    目录结构：logs/query/日期/chat_id/query_id.json
    """
    
    def __init__(self, base_dir: str = "logs/query"):
        """
        初始化查询状态记录器
        
        Args:
            base_dir: 查询状态存储的基础目录
        """
        self.base_dir = base_dir
        os.makedirs(base_dir, exist_ok=True)
    
    def _get_chat_folder(self, chat_id: str, date_str: Optional[str] = None) -> str:
        """获取chat对应的文件夹路径"""
        if date_str is None:
            date_str = datetime.now().strftime(DATE_FMT)
        
        chat_folder = os.path.join(self.base_dir, date_str, chat_id)
        os.makedirs(chat_folder, exist_ok=True)
        return chat_folder
    
    def _get_query_file_path(self, chat_id: str, query_id: str, date_str: Optional[str] = None) -> str:
        """获取查询记录文件路径"""
        chat_folder = self._get_chat_folder(chat_id, date_str)
        return os.path.join(chat_folder, f"{query_id}.json")
    
# 统一读写函数
# 读取json文件
    def _load_record(self, file_path: str) -> Dict[str, Any]:
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
# 写入json文件
    def _save_record(self, file_path: str, data: Dict[str, Any]):
        tmp_path = f"{file_path}.tmp"
        with open(tmp_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        # 原子替换
        os.replace(tmp_path, file_path)

# CRUD
    def create_query(self, chat_id: str, query_text: str, issue_scene: str,
                     dt: Optional[datetime] = None) -> str:
        """
        创建新的查询记录
        
        Args:
            chat_id: 聊天会话ID
            query_text: 查询文本
            issue_scene: 问题场景
            dt: 时间戳（可选）
            
        Returns:
            str: 生成的query_id
        """
        query_id = str(uuid.uuid4())
        timestamp = dt or datetime.now()
        
        record = QueryRecord(
            query_id=query_id,
            chat_id=chat_id,
            query_text=query_text,
            issue_scene=issue_scene,
            status=QueryStatus.PENDING.value,
            created_time=timestamp.isoformat(),
            updated_time=timestamp.isoformat(),
            followup_count=0  # 显式设置追问次数为0
        )
        
        query_file = self._get_query_file_path(chat_id, query_id)
        self._save_record(query_file, asdict(record))
        
        return query_id
    
    def update_query_status(self, chat_id: str, query_id: str, status: QueryStatus,
                            followup_prompt: Optional[str] = None,
                            result: Optional[str] = None,
                            error: Optional[str] = None,
                            dt: Optional[datetime] = None):
        """
        更新查询状态
        
        Args:
            chat_id: 聊天会话ID
            query_id: 查询ID
            status: 新状态
            followup_prompt: 追问提示（可选）
            result: 查询结果（可选）
            error: 错误信息（可选）
            dt: 时间戳（可选）
        """
        query_file = self._get_query_file_path(chat_id, query_id)
        
        if not os.path.exists(query_file):
            raise FileNotFoundError(f"Query file not found: {query_file}")
        
        query_info = self._load_record(query_file)
        
        # 更新状态
        query_info["status"] = status.value
        query_info["updated_time"] = (dt or datetime.now()).isoformat()
        
        if followup_prompt is not None:
            query_info["followup_prompt"] = followup_prompt
        if result is not None:
            query_info["result"] = result
        if error is not None:
            query_info["error"] = error
        
        self._save_record(query_file, query_info)
    
    def increment_followup_count(self, chat_id: str, query_id: str, dt: Optional[datetime] = None):
        """
        增加查询的追问次数
        
        Args:
            chat_id: 聊天会话ID
            query_id: 查询ID
            dt: 时间戳（可选）
        """
        query_file = self._get_query_file_path(chat_id, query_id)
        
        if not os.path.exists(query_file):
            raise FileNotFoundError(f"Query file not found: {query_file}")
        
        query_info = self._load_record(query_file)
        
        # 增加追问次数
        current_count = query_info.get("followup_count", 0)
        query_info["followup_count"] = current_count + 1
        query_info["updated_time"] = (dt or datetime.now()).isoformat()
        
        self._save_record(query_file, query_info)
    
    def check_followup_limit(self, chat_id: str, query_id: str, max_followup: int = 3) -> bool:
        """
        检查追问次数是否超过限制
        
        Args:
            chat_id: 聊天会话ID
            query_id: 查询ID
            max_followup: 最大追问次数，默认3次
            
        Returns:
            bool: True表示超过限制，False表示未超过限制
        """
        query_info = self.get_query_info(chat_id, query_id)
        
        if not query_info:
            return False
        
        current_count = query_info.get("followup_count", 0)
        return current_count >= max_followup
    
    def get_query_info(self, chat_id: str, query_id: str, date_str: Optional[str] = None) -> Dict[str, Any]:
        """
        获取查询信息
        
        Args:
            chat_id: 聊天会话ID
            query_id: 查询ID
            date_str: 日期字符串（格式：YYYY-MM-DD），如果为None则使用今天
            
        Returns:
            查询信息字典
        """
        query_file = self._get_query_file_path(chat_id, query_id, date_str)
        
        if os.path.exists(query_file):
            return self._load_record(query_file)
        
        return {}
    
# 列出指定状态的查询记录
    def list_queries(self, chat_id: str, statuses: Optional[Union[str, List[str]]] = None,
                     date_str: Optional[str] = None) -> List[Dict[str, Any]]:
        """列出指定状态的查询记录（按 created_time 升序）。

        Args:
            chat_id: 会话 ID
            statuses: str 或 [str]，若为 None 则返回所有
        """
        if isinstance(statuses, str):
            statuses = [statuses]

        chat_folder = self._get_chat_folder(chat_id, date_str)
        records: List[Dict[str, Any]] = []

        if os.path.exists(chat_folder):
            for fname in os.listdir(chat_folder):
                if not fname.endswith('.json'):
                    continue
                qid = fname[:-5]
                info = self.get_query_info(chat_id, qid, date_str)
                if not info:
                    continue
                if statuses is None or info.get("status") in statuses:
                    records.append(info)

        records.sort(key=lambda x: x.get("created_time", ""))
        return records

    # 兼容旧方法
    def list_pending_or_waiting(self, chat_id: str, date_str: Optional[str] = None) -> List[Dict[str, Any]]:
        """返回 PENDING + WAITING_FOLLOWUP 状态的查询（按时间升序）"""
        return self.list_queries(chat_id, [QueryStatus.PENDING.value, QueryStatus.WAITING_FOLLOWUP.value], date_str)

    def get_processing_query(self, chat_id: str, date_str: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """返回 status == PROCESSING 的第一条查询，如果没有则 None"""
        q = self.list_queries(chat_id, QueryStatus.PROCESSING.value, date_str)
        return q[0] if q else None

    def expire_waiting_queries(self, chat_id: str, timeout_minutes: int = 10, date_str: Optional[str] = None):
        """将超过等待时长的 WAITING_FOLLOWUP 查询标记为 FAILED

        Args:
            chat_id: 会话 ID
            timeout_minutes: 超时时长(分钟)。超过该时长则视为超时
            date_str: 指定日期，默认当天
        """
        now = datetime.now()
        waiting_queries = self.list_queries(chat_id, QueryStatus.WAITING_FOLLOWUP.value, date_str)
        for q in waiting_queries:
            # 解析更新时间，若失败则跳过
            ts_str = q.get("updated_time") or q.get("created_time")
            try:
                ts = datetime.fromisoformat(ts_str)
            except Exception:
                continue
            if (now - ts) > timedelta(minutes=timeout_minutes):
                # 超时，直接标记为失败，记录错误原因
                self.update_query_status(chat_id, q["query_id"], QueryStatus.FAILED, error="followup timeout")

    # 清理

    def purge(self, chat_id: str, statuses: Optional[List[str]] = None, date_str: Optional[str] = None):
        """删除指定状态的查询文件，默认 COMPLETED + FAILED"""
        if statuses is None:
            statuses = [QueryStatus.COMPLETED.value, QueryStatus.FAILED.value]
        chat_folder = self._get_chat_folder(chat_id, date_str)
        if not os.path.exists(chat_folder):
            return
        for fname in os.listdir(chat_folder):
            if not fname.endswith('.json'):
                continue
            qid = fname[:-5]
            info = self.get_query_info(chat_id, qid, date_str)
            if info.get("status") in statuses:
                os.remove(self._get_query_file_path(chat_id, qid, date_str))

    # ---- 启动时全局清理 ----
    def clear_all_queries(self):
        """删除 base_dir 下所有 query 记录（启动时调用，避免残留 PENDING）。"""
        for root, dirs, files in os.walk(self.base_dir, topdown=False):
            for file in files:
                if file.endswith('.json'):
                    os.remove(os.path.join(root, file))
            # 尝试删除空目录
            if root != self.base_dir and not os.listdir(root):
                os.rmdir(root)

    def clear_completed_and_failed(self, chat_id: str, date_str: Optional[str] = None):
        """删除 COMPLETED / FAILED 状态的记录，语义化名称"""
        self.purge(chat_id, date_str=date_str)


# 全局实例
query_logger = QueryLogger()

# 启动时清理上一轮残留
query_logger.clear_all_queries() 