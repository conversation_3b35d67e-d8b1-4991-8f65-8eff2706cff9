import os
import json
import shutil
from datetime import datetime
from typing import Optional, Dict, Any

from src.common.logs.loggers.base_logger import BaseLogger


class StateLogger(BaseLogger):
    """
    用户状态记录器
    按日期分文件夹，按chat_id生成json文件记录用户状态
    """
    
    def __init__(self, base_dir: str = "logs/state", retention_days: int = 1):
        """
        初始化状态记录器并清理过期状态文件夹

        Args:
            base_dir: 状态存储的基础目录
            retention_days: 保留最近多少天的状态，默认只保留今天
        """
        self.base_dir = base_dir
        self.retention_days = retention_days
        os.makedirs(base_dir, exist_ok=True)

        # 初始化时即进行一次过期清理，保证不会保存旧状态
        self._cleanup_old_states()

    def _cleanup_old_states(self):
        """删除过期的状态文件夹"""
        today = datetime.now().date()

        # 遍历基础目录下的所有子文件夹
        for folder in os.listdir(self.base_dir):
            folder_path = os.path.join(self.base_dir, folder)

            # 只处理日期文件夹
            if not os.path.isdir(folder_path):
                continue
            try:
                folder_date = datetime.strptime(folder, "%Y-%m-%d").date()
            except ValueError:
                # 跳过非日期格式文件夹
                continue

            # 如果文件夹日期早于保留天数范围，则删除
            if (today - folder_date).days >= self.retention_days:
                shutil.rmtree(folder_path, ignore_errors=True)
    
    def _get_date_folder(self) -> str:
        """获取当前日期的文件夹路径"""
        today = datetime.now().strftime("%Y-%m-%d")
        date_folder = os.path.join(self.base_dir, today)
        os.makedirs(date_folder, exist_ok=True)
        return date_folder
    
    def _get_state_file_path(self, chat_id: str) -> str:
        """获取状态记录文件路径"""
        date_folder = self._get_date_folder()
        return os.path.join(date_folder, f"{chat_id}.json")
    
    def init_user_state(self, chat_id: str, dt: Optional[datetime] = None):
        """
        初始化用户状态，如果文件不存在则创建
        
        Args:
            chat_id: 聊天会话ID
            dt: 时间戳（可选）
        """
        state_file = self._get_state_file_path(chat_id)
        
        # 如果文件不存在，则创建初始状态
        if not os.path.exists(state_file):
            state_info = {
                "timestamp": (dt or datetime.now()).isoformat(),
                "user_state": "none"
            }
            
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_info, f, ensure_ascii=False, indent=2)
    
    def update_user_state(self, chat_id: str, user_state: str, dt: Optional[datetime] = None):
        """
        更新用户状态
        
        Args:
            chat_id: 聊天会话ID
            user_state: 用户状态（中文功能名）
            dt: 时间戳（可选）
        """
        state_file = self._get_state_file_path(chat_id)
        
        # 确保文件存在
        self.init_user_state(chat_id, dt)
        
        # 更新状态
        state_info = {
            "timestamp": (dt or datetime.now()).isoformat(),
            "user_state": user_state
        }
        
        with open(state_file, 'w', encoding='utf-8') as f:
            json.dump(state_info, f, ensure_ascii=False, indent=2)
    
    def get_user_state(self, chat_id: str, date_str: Optional[str] = None) -> Dict[str, Any]:
        """
        获取用户状态
        
        Args:
            chat_id: 聊天会话ID
            date_str: 日期字符串（格式：YYYY-MM-DD），如果为None则使用今天
            
        Returns:
            用户状态字典
        """
        if date_str is None:
            date_str = datetime.now().strftime("%Y-%m-%d")
        
        state_file = os.path.join(self.base_dir, date_str, f"{chat_id}.json")
        
        if os.path.exists(state_file):
            try:
                with open(state_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        # 如果文件不存在或读取失败，返回默认状态
        return {
            "timestamp": datetime.now().isoformat(),
            "user_state": "none"
        }

    def get_current_state_str(self, chat_id: str, date_str: Optional[str] = None) -> str:
        """
        获取当前状态字符串
        
        Args:
            chat_id: 聊天会话ID
            date_str: 日期字符串（格式：YYYY-MM-DD），如果为None则使用今天
            
        Returns:
            str: 当前状态字符串
        """
        try:
            # 获取状态记录
            state_info = self.get_user_state(chat_id, date_str)
            
            return state_info.get("user_state", "none")
            
        except Exception as e:
            from src.common.logs.logger import app_logger
            app_logger.error(f"获取当前状态失败: {str(e)}")
            return "none"


# 全局实例
state_logger = StateLogger() 