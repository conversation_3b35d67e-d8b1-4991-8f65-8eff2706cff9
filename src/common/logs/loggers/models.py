from dataclasses import dataclass, asdict, field
from typing import Dict

@dataclass
class Event:
    """
    埋点事件数据结构
    """
    timestamp: str  # 日期字符串
    event_type: str # 事件类型
    en_name: str = ""  # 用户名-英文
    cn_name: str = "" # 用户名-中文
    event_params: Dict = field(default_factory=dict)

    def to_dict(self) -> dict:
        """
        转换为字典，方便序列化写入文件
        """
        return asdict(self)

    @staticmethod
    def from_dict(data: dict) -> "Event":
        """
        从字典反序列化为 Event 对象
        """
        return Event(**data)


@dataclass
class Evaluate:
    """
    评价数据结构
    """
    timestamp: str # 时间
    ticket_id: str  # 工单ID
    evaluate: str # 满意度
    en_name: str # 用户名-英文
    cn_name: str # 用户名-中文

    def to_dict(self) -> dict:
        """
        转换为字典，方便序列化写入文件
        """
        return asdict(self)

    @staticmethod
    def from_dict(data: dict) -> "Evaluate":
        """
        从字典反序列化为 Evaluate 对象
        """
        return Evaluate(**data)
