from enum import Enum, unique
from functools import wraps

@unique
class ErrorCode(Enum):
    INVALID_PARAMETER = 1000
    MODEL_OVERLOAD = 1001
    MODEL_STREAM_RESULT_EMPTY = 1002
    LOG_DOWNLOAD_LINK_INVALID_NO_DECRYPTED_URL = 1003
    LOG_DOWNLOAD_LINK_MISSING_INDEX_KEY = 1004
    LOG_DOWNLOAD_LINK_EMPTY = 1005
    NO_VALID_LOG_FOLDER_IN_EXTRACTED_DIR = 1006
    LOG_FILE_PATH_NOT_FOUND = 1007
    LOG_PATH_IS_EMPTY = 1008
    LOG_FOLDER_EMPTY_NO_LOG_FILES_FOUND = 1009
    LOG_FILE_FORMATTING_FAILED = 1010
    MISSING_REQUIRED_FIELD = 1011
    UNKNOWN_SCENE = 1012
    USER_ACTION_NOT_FOUND = 1013
    PRESET_SCENES_EMPTY = 1014
    USER_INTENT_PROMPT_NOT_FOUND = 1015
    USER_INTENT_SCENE_NOT_FOUND = 1016
    INVALID_IWIKI_URL = 1017
    IWIKI_API_ERROR = 1018
    NO_CODE_BLOCK = 1019
    REGEX_ERROR = 1020
    PROMPT_FORMAT_ERROR = 1021
    IWIKI_PARSE_ERROR = 1022
    WECOM_WEBHOOK_ERROR = 1023
    BEACON_API_ERROR = 1025         # Beacon API 调用错误
    BEACON_RESPONSE_ERROR = 1026    # Beacon API 响应解析错误
    TEDI_API_ERROR = 1027           # Tedi API 调用错误
    TEDI_RESPONSE_ERROR = 1028      # Tedi API 响应解析错误 
    PARAMETER_PARSE_FAILED = 1029    # 参数解析失败


ERROR_MESSAGES = {
    ErrorCode.INVALID_PARAMETER: "参数错误",
    ErrorCode.MODEL_OVERLOAD: "大模型过载",
    ErrorCode.MODEL_STREAM_RESULT_EMPTY: "模型流式返回结果为空",
    ErrorCode.LOG_DOWNLOAD_LINK_INVALID_NO_DECRYPTED_URL: "日志下载链接无效，未获取到解密后的链接",
    ErrorCode.LOG_DOWNLOAD_LINK_MISSING_INDEX_KEY: "日志下载链接中未找到有效的 index key",
    ErrorCode.LOG_DOWNLOAD_LINK_EMPTY: "日志下载链接为空",
    ErrorCode.NO_VALID_LOG_FOLDER_IN_EXTRACTED_DIR: "在解压目录中未找到有效的日志文件夹",
    ErrorCode.LOG_FILE_PATH_NOT_FOUND: "未找到日志文件地址",
    ErrorCode.LOG_PATH_IS_EMPTY: "日志路径为空",
    ErrorCode.LOG_FOLDER_EMPTY_NO_LOG_FILES_FOUND: "日志文件夹为空，未找到日志文件",
    ErrorCode.LOG_FILE_FORMATTING_FAILED: "格式化日志文件失败",
    ErrorCode.MISSING_REQUIRED_FIELD: "缺少必填的字段",
    ErrorCode.UNKNOWN_SCENE: "未识别到场景",
    ErrorCode.USER_ACTION_NOT_FOUND: "未识别到预设场景",
    ErrorCode.PRESET_SCENES_EMPTY: "预设场景配置为空",
    ErrorCode.USER_INTENT_PROMPT_NOT_FOUND: "未找到用户意图分析Prompt",
    ErrorCode.USER_INTENT_SCENE_NOT_FOUND: "未找到用户意图分析场景",
    ErrorCode.INVALID_IWIKI_URL: "无效的 iWiki 链接",
    ErrorCode.IWIKI_API_ERROR: "iWiki API 请求错误",
    ErrorCode.NO_CODE_BLOCK: "解析格式失败。内容以```开头，但未找到代码块",
    ErrorCode.REGEX_ERROR: "正则表达式错误",
    ErrorCode.PROMPT_FORMAT_ERROR: "Prompt 格式错误",
    ErrorCode.IWIKI_PARSE_ERROR: "iWiki 解析错误",
    ErrorCode.WECOM_WEBHOOK_ERROR: "企业微信 webhook 错误",
    ErrorCode.BEACON_API_ERROR: "Beacon API 请求错误",
    ErrorCode.BEACON_RESPONSE_ERROR: "Beacon API 响应解析错误",
    ErrorCode.TEDI_API_ERROR: "Tedi API 请求错误",
    ErrorCode.TEDI_RESPONSE_ERROR: "Tedi API 响应解析错误",
    ErrorCode.PARAMETER_PARSE_FAILED: "参数解析失败"
}


class DetailedValueError(ValueError):
    def __init__(self, code: ErrorCode, message=None, context=None):
        if message is None:
            message = ERROR_MESSAGES.get(code, "未知错误")
        self.message = message
        self.code = code
        self.context = context or {}

    def __str__(self):
        return f"{self.message} (code={self.code.name}[{self.code.value}], context={self.context})"


def raise_value_error(code: ErrorCode, message=None, context=None):
    raise DetailedValueError(code=code, message=message, context=context)


def unified_exception_handler(return_type="agent_result"):
    """
    统一异常处理装饰器
    
    Args:
        return_type: 返回类型，"agent_result" 或 "business_result"
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except DetailedValueError as e:
                # 处理自定义错误，保留具体错误信息
                error_message = str(e)
                if hasattr(e, 'message'):
                    error_message = e.message
                
                if return_type == "agent_result":
                    from src.common.models.agent_result import AgentResult
                    return AgentResult.error(error_message)
                else:  # business_result
                    from src.common.models.business_result import BusinessResult
                    return BusinessResult.fail(error_message)
                    
            except Exception as e:
                # 处理其他异常，统一返回通用错误信息
                from src.common.logs.logger import app_logger
                app_logger.error(f"未预期的异常: {str(e)}", exc_info=True)
                
                generic_error = "抱歉，大模型服务繁忙，请稍后重试。如果问题持续存在，请联系技术支持。"
                
                if return_type == "agent_result":
                    from src.common.models.agent_result import AgentResult
                    return AgentResult.error(generic_error)
                else:  # business_result
                    from src.common.models.business_result import BusinessResult
                    return BusinessResult.fail(generic_error)
        
        return wrapper
    return decorator
