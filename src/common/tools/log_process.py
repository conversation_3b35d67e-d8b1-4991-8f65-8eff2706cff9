# 1. 清晰日志数据，排除不需要的内容，降低token 消耗率
# 2. 结构化日志内容支持按时间维度、关键字维度查询日志内容
# 3. 支持关键字提取，组件关键相关的知识库。（对于日志而言，不需要太复杂的知识库，直接查文件也很快）
import re
from datetime import datetime
import json
from typing import List, Tuple, Dict, Optional, Callable, Any

from src.common.logs.logger import app_logger
from src.common.error.detailed_value_error import raise_value_error, ErrorCode


class LogProcessor:

    def __init__(self):
        self._formatted_logs = []
        
    def __convert_to_timestamp__(self, time_str):
    # 直接解析为本地时间（无时区信息）
        dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
    # 生成时间戳（根据系统时区自动转换到UTC）
        return int(dt.timestamp() * 1000)

    # 根据正则表达式匹配结果构建日志项字典。
    # 提取日志级别、时间、线程ID、进程ID、线程名、进程名、标签和日志内容，并返回一个字典。
    def __buildLogItem__(self, match):
        log_time = match[2].replace(' ','').replace('+80',' ').replace('+90', ' ')
        return {
            "log_level": match[1],
            "log_time": log_time,
            "timestamp": self.__convert_to_timestamp__(log_time),
            "tid": match[3],
            "pid": match[4],
            "t_name": match[5],
            "p_name": match[6],
            "tag": match[7].replace('[','').replace(']',''),
            "log_content": match[8]
        }
        
    # 用于格式化日志文件，参数 filePath，表示日志文件的路径。
    # 返回一个 格式化后的 日志列表
    def __formatLogs__(self,filePath):
        try:
            formatted_logs = []
            # 日志提取，正则表达式
            log_pattern = r"""
            \[(?P<log_level>[\w])\]                           # 日志级别
            \[(?P<log_time>\d{4}-\d{2}-\d{2}\s\+\d{2}\s\d{2}:\d{2}:\d{2}\.\d{3})\]  # 时间
            \[(?P<tid>\d+),\s(?P<pid>[^\]]+)\]                # 线程ID和进程ID，进程ID可以包含非数字字符
            \[(?P<t_name>[^\[\]]+(?:\[[^\[\]]+\])*)\]         # 线程名
            \[(?P<p_name>[^\]]+)\]                            # 进程名
            # \[(?P<tag>[^\[\]]+(?:\[[^\[\]]+\])*)*\]         # 标签，支持嵌套方括号
            \[(?P<tag>.*)\]                                  # 标签
            (?:\[\])*                                        # 匹配零个或多个空的方括号
            \[(?P<log_content>.*)                             # 日志内容
            """
            
            dumplicated_cotent = set()
            pattern = re.compile(log_pattern, re.VERBOSE)
            
            def filterDumplicatedLine(line):
                match = re.match(pattern, line)
                if match:
                    formatedItem = self.__buildLogItem__(match)
                    logContent = formatedItem['log_content']
                    dumplicated_cotent.add(logContent)
                    formatted_logs.append(formatedItem)
            
            with open(filePath, 'r', encoding='utf-8', errors='ignore') as f:
                none_normal_lines = []
                pre_line = ''
                for line_num, line in enumerate(f, 1):
                    if line.startswith('['):
                        if len(none_normal_lines) > 0:
                            log_line = ''.join(none_normal_lines)
                            log_line = log_line + "\n"
                            log_line = pre_line.replace('\n', '') + log_line
                            filterDumplicatedLine(log_line)
                            none_normal_lines.clear()
                            pre_line = ''
                        else:
                            # 说明不是多行，则直接写入上一行
                            if pre_line != '':
                                filterDumplicatedLine(pre_line)
                        # 记录 pre_line
                        pre_line = line
                    else:
                        none_normal_lines.append(line.replace("\n", ''))
                # 最后一行不为空，则写入
                if pre_line != '':
                    filterDumplicatedLine(pre_line)
        except Exception as e:
            # raise ValueError(f"格式化日志文件失败: {e}")
            raise_value_error(ErrorCode.LOG_FILE_FORMATTING_FAILED, f"格式化日志文件失败: {e}")
        return formatted_logs
    

    # 处理指定路径的日志文件，调用 __formatLogs__ 方法并将结果存储在 self._formatted_logs 中。
    def process(self, filePath):
        formatLogs = self.__formatLogs__(filePath)
        self._formatted_logs = formatLogs
    
    # 根据给定的 标签 过滤日志。
    # 遍历 self._formatted_logs，检查每个日志项的标签、线程名和日志内容是否匹配给定的标签。
    # 如果匹配，将格式化的日志内容添加到 rebuildLogs 列表中。
    def filter_log_by_tags(self, tags):
        rebuildLogs = []
        for i in self._formatted_logs:
            log_tag = i['tag']
            t_name = i['t_name']
            log_time = i["log_time"]
            log_level = i["log_level"]
            log_content = i["log_content"]
            for tag in tags:
                pattern = re.compile(tag)
                if pattern.search(log_tag) or pattern.search(t_name) or pattern.search(log_content):
                    rebuildLogs.append(
                        f"{log_time} {log_level} {log_tag} {log_content}\n"
                    )
        return rebuildLogs
    
    # 在日志行中查找特定的索引关键字。
    # 定义多个正则表达式模式来匹配不同格式的关键字。
    # 如果找到匹配项，返回匹配的内容；否则返回 None。
    def findIndexKeyFromLog(self, indexKey, logLine):
        # indexKey:数字 indexKey=数字 indexKey:-数字 indexKey=-数字 indexKey = 数字 indexKey = -数字
        patterns = [f"({indexKey}: \d+)", f"({indexKey}: -\d+)", f"({indexKey}:\d+)", f"({indexKey}:-\d+)", 
                    f"({indexKey}=\d+)", f"({indexKey}=-\d+)",f"({indexKey} = -\d+)",f"({indexKey} = \d+)"]
        for pattern in patterns:
            p = re.compile(pattern)
            match = re.search(p, logLine)
            if match:
                return match[1]
        return None

    # 根据给定的 标签 和 时间 过滤日志。
    # startTime（如 2025-02-17 10:24:07），先按时间过滤，再按tags过滤
    # 遍历 self._formatted_logs，检查每个日志项的标签、线程名和日志内容是否匹配给定的标签。
    # 如果匹配，将格式化的日志内容添加到 rebuildLogs 列表中。
    def filter_log_by_tags_and_time(self, startTime=None, endTime=None, tags=None):
        # 过滤后的日志列表
        rebuildLogs = []

        if tags is None:
            tags = []

        # 时间过滤开关和时间转换
        time_filter_enabled = False
        start_time_dt = None
        end_time_dt = None
        if startTime is not None and endTime is not None:
            # start_time_dt = datetime.strptime(startTime, "%Y-%m-%d %H:%M:%S.%f")
            # end_time_dt = datetime.strptime(endTime, "%Y-%m-%d %H:%M:%S.%f")
            time_filter_enabled = True

        # 预编译tags，失败退化为字符串匹配
        compiled_tags = []
        for tag_pattern in tags:
            try:
                pattern = re.compile(tag_pattern)
                compiled_tags.append((pattern, True))  # True表示正则
            except re.error:
                compiled_tags.append((tag_pattern, False))  # False表示普通字符串

        for log in self._formatted_logs:
            log_tag = log['tag']
            t_name = log['t_name']
            log_time = log["log_time"]
            log_level = log["log_level"]
            log_content = log["log_content"]

            # 转换日志时间
            try:
                log_time_dt = datetime.strptime(log_time, "%Y-%m-%d %H:%M:%S.%f")
            except Exception:
                # 时间格式异常，跳过该日志
                continue

            # 时间过滤
            if time_filter_enabled:
                if not (startTime <= log_time_dt <= endTime):
                    continue

            # 标签匹配
            for pattern, is_regex in compiled_tags:
                if is_regex:
                    if pattern.search(log_tag) or pattern.search(t_name) or pattern.search(log_content):
                        rebuildLogs.append(f"{log_time} {log_level} {log_tag} {log_content}\n")
                        break
                else:
                    if (pattern in log_tag) or (pattern in t_name) or (pattern in log_content):
                        rebuildLogs.append(f"{log_time} {log_level} {log_tag} {log_content}\n")
                        break

        return rebuildLogs
    
    # 删除日志内容部分的 "at com." 后面的内容
    def remove_at_com_content(self, filteredLogs):
        updated_logs = []
        for log in filteredLogs:
            # 日志格式为 "年月日 时间 级别 tag 内容"
            parts = log.split(' ', 4)
            if len(parts) < 5:
                continue  # 跳过格式不正确的日志行
            log_time, log_level, log_tag, log_content = parts[0] + ' ' + parts[1], parts[2], parts[3], parts[4]
            # 删除 "at com." 后面的内容
            # todo 堆栈信息，也有at开头的其他标志
            log_content = re.sub(r'at com\..*', '', log_content)
            updated_log = f"{log_time} {log_level} {log_tag} {log_content}"
            updated_logs.append(updated_log)
        return updated_logs


    # Direct:false send req retCode:-31
    # Direct:true send req retCode:-31
    # CommReq handleException:javax.net.ssl.SSLHandshakeException: SSL handshake aborted: ssl=0x6f0e85ee08: I/O error during system call, Connection reset by peer	at com.android.org.conscrypt.NativeCrypto.SSL_do_handshake(Native Method)
    # CommReq handleException:java.net.ConnectException: Failed to connect to dd.myapp.com/182.40.55.61:443	at okhttp3.internal.connection.RealConnection.connectSocket(Unknown Source:141)	
    def remove_duplicates_content(self, filteredLogs, duplicates_contents):
        updated_logs = []
        seen_contents = []  # 用于跟踪已经出现过的 duplicates_contents 中的内容

        for log in filteredLogs:
            # 日志格式为 "年月日 时间 级别 tag 内容"
            parts = log.split(' ', 4)
            if len(parts) < 5:
                continue  # 跳过格式不正确的日志行
            log_time, log_level, log_tag, log_content = parts[0] + ' ' + parts[1], parts[2], parts[3], parts[4]
            updated_log = f"{log_time} {log_level} {log_tag} {log_content}"
            
            # 检查 log_content 中是否包含 duplicates_contents 中的任何内容
            matched_duplicate = None
            for duplicate in duplicates_contents:
                if duplicate in log_content:
                    matched_duplicate = duplicate
                    break
            
            if matched_duplicate:
                if matched_duplicate not in seen_contents:
                    updated_logs.append(updated_log)
                    seen_contents.append(matched_duplicate)
            else:
                updated_logs.append(updated_log)
        
        return updated_logs
    
    # 日志的过滤
    # 根据 tag 和 key 进行去重
    # 可选参数：keyValue的分隔符
    # 如 retCode:-31, key 为 retCode
    # 可设置 : 和 , 来获取 retCode 值（keyValue）为 -31
    # 用于删除重复的日志，同时也保留 keyValue 不同的日志。
    # duplicates_contents示例如下，默认的 分隔符对 为 "=" 和 ","
    # duplicates_contents = [('halley-downloader-SectionTransport', 'retCode'), ('CommReq', 'handleException', ":", ":")]
    
    # 参考日志行
    # Direct:true send req retCode:-31,
    # CommReq handleException:javax.net.ssl.SSLHandshakeException: SSL handshake aborted: ssl=0x6f0e85ee08: I/O error during system call, Connection reset by peer	at com.android.org.conscrypt.NativeCrypto.SSL_do_handshake(Native Method)
    # 日志格式：年月日 时间 级别 tag log_content。如下：
    # 2025-02-16 15:25:59.204 I halley-downloader-SectionTransport 1-538A000AF07F75755411BC1DFEF72719:[20|sche] Direct:false send req retCode:-31,msg:javax.net.ssl.SSLHandshakeException:SSL handshake aborted: ssl=0x6ed896b088: I/O error during system call, Connection reset by peer
    # 根据tag 过滤其内容的某个key
    # tag ： halley-downloader-SectionTransport
    # log_content中的key：retCode
    # 可能值：retCode:-31 、retCode:-24、 retCode:0
    # 功能：过滤重复内容值，只允许每个值出现一次。
    # 参数：filteredLogs 原始日志
    # 参数：duplicates_contents 过滤规则：[(tag1, key1),(tag2, key2),(tag3, key3)]
    def remove_duplicates_content_with_tag_key(self, filteredLogs, duplicates_contents):
        # 默认的分隔符，用于分割 log_content 中 key的前半部分和后半部分
        default_key_value_separator="="
        # 默认的分隔符，用于取到 {key}{default_key_value_separator} 到 {default_item_separator} 之间的内容
        default_item_separator=","
        updated_logs = []
        seen_values = {}  # 用于跟踪每个 tag-key 对应的已经出现过的值

        for log in filteredLogs:
            # 解决相同tag 但key不同 判断错误的问题
            is_matched_key = False
            is_matched_tag = False
            # 日志格式为 "年月日 时间 级别 tag 内容"
            parts = log.split(' ', 4)
            if len(parts) < 5:
                continue  # 跳过格式不正确的日志行
            log_time, log_level, log_tag, log_content = parts[0] + ' ' + parts[1], parts[2], parts[3], parts[4]
            updated_log = f"{log_time} {log_level} {log_tag} {log_content}"
            
            # 检查 log_tag 是否在 duplicates_contents 中指定
            for tag, key, *separators in duplicates_contents:
                # *separators 获取可选的分隔符。如果提供了分隔符，则使用它们；否则，使用默认分隔符。
                key_value_separator = separators[0] if len(separators) > 0 else default_key_value_separator
                item_separator = separators[1] if len(separators) > 1 else default_item_separator
                
                # 如果是匹配tag
                if tag in log_tag:
                    is_matched_tag = True
                    # 提取 key 的值
                    key_value = None
                    # 情况1 {key}{key_value_separator} 一样，但{key}{key_value_separator}后的内容（key_value）不一样，不认为重复 
                    if f"{key}{key_value_separator}" in log_content:
                        key_part = log_content.split(f"{key}{key_value_separator}")[1].strip()
                        # key_value: key 的值
                        # 将 key_part 字符串中的内容按照 item_separator 分割成一个列表
                        key_value = key_part.split(item_separator)[0]
                        is_matched_key = True
                    # 情况2 只要出现 key 就是重复
                    elif key in log_content:
                        key_value = key
                        is_matched_key = True
                    
                    if key_value:
                        # 初始化 seen_values 中的集合
                        if (tag, key) not in seen_values:
                            seen_values[(tag, key)] = set()
                        
                        # 检查 key 的值是否已经记录过
                        if key_value not in seen_values[(tag, key)]:
                            updated_logs.append(updated_log)
                            seen_values[(tag, key)].add(key_value)
                    # 相同tag key不同，break会跳过相同的tag
                    # break  # 找到匹配的 tag 后不再继续检查其他规则

            if not is_matched_tag or not is_matched_key:
                updated_logs.append(updated_log)
        return updated_logs
    
    def delete_log_by_tags_and_content(self, filteredLogs, delete_logs, is_fuzzy_match_tag=False):
        """
        根据标签和内容删除日志，支持正则匹配。
        标签匹配支持模糊匹配（re.search）或精确匹配（re.fullmatch）。
        如果正则编译失败，退化为简单字符串比较。

        :param filteredLogs: 日志列表，每条日志格式为 "年月日 时间 级别 tag 内容"
        :param delete_logs: 过滤规则列表 [(tag_pattern, content_pattern), ...]
        :param is_fuzzy_match_tag: 标签是否模糊匹配，True 用 re.search，False 用 re.fullmatch
        :return: 删除匹配日志后的剩余日志列表

        如：
        tag： DownloadTag
        log_content包含： fileType=PLUGIN
        删除 DownloadTag 中有包含 fileType=PLUGIN 的日志行
        """
        remaining_logs = []

        compiled_delete_logs = []
        for tag_pattern, content_pattern in delete_logs:
            try:
                tag_re = re.compile(tag_pattern)
            except re.error:
                tag_re = None
            try:
                content_re = re.compile(content_pattern)
            except re.error:
                content_re = None
            compiled_delete_logs.append((tag_re, content_re, tag_pattern, content_pattern))

        for log in filteredLogs:
            log_dict = self.parse_log_line(log)
            if not log_dict:
                continue
            log_tag = log_dict['tag']
            log_content = log_dict['content']

            match_found = False
            for tag_re, content_re, tag_pattern, content_pattern in compiled_delete_logs:
                # 标签匹配
                if tag_re:
                    if is_fuzzy_match_tag:
                        tag_match = bool(tag_re.search(log_tag))
                    else:
                        tag_match = bool(tag_re.fullmatch(log_tag))
                else:
                    if is_fuzzy_match_tag:
                        tag_match = tag_pattern in log_tag
                    else:
                        tag_match = (log_tag == tag_pattern)

                # 内容匹配
                if content_re:
                    content_match = bool(content_re.search(log_content))
                else:
                    content_match = (content_pattern in log_content)

                if tag_match and content_match:
                    match_found = True
                    break

            if not match_found:
                remaining_logs.append(log)

        return remaining_logs

    def save_log_by_tags_and_content(
            self,
            filtered_logs: List[str],
            save_logs: List[Tuple[str, str]],
            split_info: Optional[List[Tuple[str, str]]] = None,
            is_fuzzy_match_tag: bool = False,
            dedup_targets: Optional[List[Tuple[str, str]]] = None,
            tag_ignore_patterns: Optional[Dict[str, List[str]]] = None,
            placeholder: str = ""
        ) -> List[str]:
        """
        根据标签和内容筛选日志，支持指定 (tag, content) 组合的全局去重，其他只去除连续重复。
        支持 split_info 截断日志内容。
        标签和内容匹配支持正则表达式。
        支持根据tag对应的忽略正则表达式对内容归一化后去重。

        :param filtered_logs: 过滤后的日志列表，每条日志格式为 "年月日 时间 级别 tag 内容"
        :param save_logs: 需要保存的标签和内容列表，格式为 [(tag_regex, content_regex), ...]
        :param split_info: [(split_tag_regex, split_key), ...]，用于截断日志内容
        :param is_fuzzy_match_tag: 是否模糊匹配标签（模糊匹配时用 re.search，否则用 re.fullmatch）
        :param dedup_targets: 需要全局去重的 (tag_regex, content_regex) 列表
        :param tag_ignore_patterns: dict，key是tag字符串，value是对应的忽略正则表达式列表
        :param placeholder: 替换匹配内容的占位符，默认""
        :return: 过滤后的关键日志列表
        """
        save_logs = save_logs or []
        split_info = split_info or []
        dedup_targets = dedup_targets or []
        tag_ignore_patterns = tag_ignore_patterns or {}

        # 预编译忽略正则表达式，增加异常捕获
        compiled_ignore_patterns = []  # List of tuples: (compiled_tag_pattern, original_tag_key, [compiled_ignore_regex])
        for tag_key, patterns in tag_ignore_patterns.items():
            try:
                compiled_tag_pattern = re.compile(tag_key)
            except re.error as e:
                raise_value_error(ErrorCode.REGEX_ERROR, f"编译tag_ignore_patterns的tag正则失败，tag={tag_key}, error={e}")
            compiled_list = []
            for p in patterns:
                try:
                    compiled_list.append(re.compile(p))
                except re.error as e:
                    raise_value_error(ErrorCode.REGEX_ERROR, f"编译忽略正则失败，tag={tag_key}, pattern={p}, error={e}")
            compiled_ignore_patterns.append((compiled_tag_pattern, tag_key, compiled_list))

        def tag_match(log_tag: str, target_tag_pattern: str) -> bool:
            try:
                pattern = re.compile(target_tag_pattern)
            except re.error:
                if is_fuzzy_match_tag:
                    return target_tag_pattern in log_tag
                else:
                    return log_tag == target_tag_pattern

            if is_fuzzy_match_tag:
                return bool(pattern.search(log_tag))
            else:
                return bool(pattern.fullmatch(log_tag))

        def content_match(log_content: str, target_content_pattern: str) -> bool:
            try:
                pattern = re.compile(target_content_pattern)
            except re.error:
                return target_content_pattern in log_content
            return bool(pattern.search(log_content))

        def should_save(log_tag: str, log_content: str) -> Optional[Tuple[str, str]]:
            for tag_pattern, content_pattern in save_logs:
                if tag_match(log_tag, tag_pattern) and content_match(log_content, content_pattern):
                    return tag_pattern, content_pattern
            return None

        def needs_dedup(log_tag: str, log_content: str) -> bool:
            for d_tag_pattern, d_content_pattern in dedup_targets:
                if tag_match(log_tag, d_tag_pattern) and content_match(log_content, d_content_pattern):
                    return True
            return False

        def normalize_content(tag: str, content: Optional[str]) -> str:
            if content is None:
                return ""
            # 先尝试用正则匹配tag_ignore_patterns的key
            patterns = None
            for compiled_tag_pattern, original_tag_key, ignore_regex_list in compiled_ignore_patterns:
                try:
                    if compiled_tag_pattern.fullmatch(tag):
                        patterns = ignore_regex_list
                        break
                except Exception:
                    # 正则匹配异常时忽略，继续尝试下一个
                    continue
            # 如果正则匹配失败，退化成普通字符串匹配
            if patterns is None:
                for _, original_tag_key, ignore_regex_list in compiled_ignore_patterns:
                    if tag == original_tag_key:
                        patterns = ignore_regex_list
                        break

            if not patterns:
                return content

            normalized = content
            for p in patterns:
                try:
                    normalized = p.sub(placeholder, normalized)
                except Exception as e:
                    raise_value_error(ErrorCode.REGEX_ERROR, f"正则替换失败，tag={tag}, pattern={p.pattern}, error={e}")
            return normalized

        key_logs = []
        last_log_content_map = {}  # tag_pattern -> last normalized content (连续去重)
        dedup_content_map = {}     # tag_pattern -> set of normalized contents (全局去重)

        for log in filtered_logs:
            log_dict = self.parse_log_line(log)
            if not log_dict:
                continue

            log_tag = log_dict['tag']
            log_content = log_dict['content']

            # 处理 split_info 截断
            for split_tag_pattern, split_key in split_info:
                if tag_match(log_tag, split_tag_pattern) and split_key in log_content:
                    log_content = log_content.split(split_key, 1)[0]
                    break

            matched = should_save(log_tag, log_content)
            if not matched:
                continue

            tag_pattern, _ = matched

            # 归一化内容
            normalized_content = normalize_content(log_tag, log_content)

            if needs_dedup(log_tag, log_content):
                dedup_content_map.setdefault(tag_pattern, set())
                if normalized_content in dedup_content_map[tag_pattern]:
                    continue
                dedup_content_map[tag_pattern].add(normalized_content)
            else:
                if last_log_content_map.get(tag_pattern) == normalized_content:
                    continue
                last_log_content_map[tag_pattern] = normalized_content

            # 重新拼接日志行（如果内容被截断）
            if log_content != log_dict['content']:
                log = f"{log_dict['time']} {log_dict['level']} {log_tag} {log_content}\n"

            key_logs.append(log)

        return key_logs

    def parse_log_line(self, log_line: str) -> Optional[Dict[str, str]]:
        """
        解析单条日志，返回字典包含时间、级别、tag、内容。
        日志格式示例：
        "2025-04-19 00:01:52.930 I ReceivingRewardViewModel|00:01.52.926|KLog ReceivingRewardViewModel]:doQueryLotteryResult item: ..."
        """
        parts = log_line.split(' ', 4)
        if len(parts) < 5:
            return None
        return {
            'time': parts[0] + ' ' + parts[1],
            'level': parts[2],
            'tag': parts[3],
            'content': parts[4]
        }

    def unique_json_append(self, data_list: List[Dict], item: Any, seen: set) -> bool:
        """
        将 item 转成 json 字符串去重后追加到 data_list。
        返回是否添加成功。
        """
        item_str = json.dumps(item, sort_keys=True, ensure_ascii=False)
        if item_str not in seen:
            seen.add(item_str)
            data_list.append(item)
            return True
        return False

    def format_log_to_info(
        self,
        filtered_logs: List[str],
        keyword: str,
        extract_func: Callable[[str], Optional[Any]],
        log_desc: str = ""
    ) -> str:
        """
        通用日志格式化函数。
        :param filtered_logs: 日志列表
        :param keyword: 过滤关键字
        :param extract_func: 提取函数，传入日志内容，返回结构化数据或 None
        :param log_desc: 日志描述，用于打印
        :return: JSON字符串
        """
        all_data = []
        seen = set()

        for log in filtered_logs:
            parsed = self.parse_log_line(log)
            if not parsed:
                continue
            content = parsed['content']
            if keyword not in content:
                continue
            info = extract_func(content)
            if info is None:
                continue
            self.unique_json_append(all_data, info, seen)

        app_logger.info(f"{log_desc} 共提取 {len(all_data)} 条不重复记录")
        return json.dumps(all_data, ensure_ascii=False, indent=2)


    def extract_download_info(self, log_content: str) -> Optional[Dict]:
        """
        提取 DownloadInfo 信息，返回字典。
        示例日志内容：
        DownloadInfo{downloadState=PAUSED, appId=53987000, apkId=129515448, downloadTicket=129515448, packageName='com.xiaoe.client', name='小鹅通学员版', versionCode=905, versionName='5.12.0', apkUrlList=[https://xiaoetong-1252524126.cdn.xiaoeknow.com/APP_builder_files/XiaoeApp-v5.8.5-881-xiaoe.apk]}
        """
        match = re.search(r'DownloadInfo\{(.+)\}$', log_content)
        if not match:
            return None

        content = match.group(1)
        fields = ["appId", "apkId", "downloadTicket", "packageName", "name", "versionCode",
                "versionName", "apkUrlList", "scene", "statScene"]

        result = {}
        for field in fields:
            if field == "apkUrlList":
                m = re.search(r'apkUrlList=\[([^\]]*)\]', content)
                if m:
                    urls = [url.strip() for url in m.group(1).split(',') if url.strip()]
                    result[field] = urls
                else:
                    result[field] = []
            else:
                # 先匹配字符串类型（带单引号）
                m_str = re.search(rf"{field}='([^']*)'", content)
                if m_str:
                    result[field] = m_str.group(1)
                    continue
                # 匹配数字或其他非逗号字符串
                m_val = re.search(rf"{field}=([^,]+)", content)
                if m_val:
                    val = m_val.group(1).strip()
                    # 尝试转数字
                    if val and val.isdigit():
                        val = int(val)
                    else:
                        try:
                            val = float(val)
                        except ValueError:
                            pass
                    result[field] = val
                else:
                    result[field] = None

        # 判断 versionName 是否包含在 apkUrlList 第一个 URL 中
        if result.get('apkUrlList') and result.get('versionName'):
            result["isInfoMatch"] = 'true' if result['versionName'] in result['apkUrlList'][0] else 'false'
        else:
            result["isInfoMatch"] = 'false'

        return result


    def extract_lottery_item_info(self, log_content: str) -> Optional[Dict]:
        """
        提取活动页 我的奖品item 信息，只返回 orderStatus=3 的列表。
        示例日志内容：
        doQueryLotteryResult item: PropertyActBtnConfig(name=应用宝专属基地装扮（7天）*1..., time=2025-04-17 10:06:15, orderId=GF-25-20250417100615-18ekl4, propertyBaseType=2, propertyPlatId=4, propertyPlatType=0, orderStatus=3, hasUserAddr=false, ...)
        """
        # 匹配所有 PropertyActBtnConfig(...) 的内容
        items = re.findall(r'PropertyActBtnConfig\((.*?)\)(?:,|$)', log_content, re.DOTALL)
        if not items:
            return None

        fields = ['name', 'orderId', 'orderStatus', 'sendErrorCode']

        for content in items:
            record = {}
            for field in fields:
                if field == 'name':
                    # name 字段可能包含逗号，匹配到下一个字段名或结尾
                    m = re.search(rf'{field}=(.*?)(?=, \w+=|$)', content)
                else:
                    m = re.search(rf'{field}=([^,]*)(?:,|$)', content)
                record[field] = m.group(1).strip() if m else None

            # 只保留 orderStatus=3 的记录
            if record.get('orderStatus') == '3':
                return record
        return None


    def extract_obtain_present_info(self, log_content: str) -> Optional[Dict]:
        """
        提取奖品领取结果信息，返回列表。
        示例日志内容：
        ReceiveResult(showResult=ShowResult(title=领取奖励失败, desc=亲密玫瑰x1, img=, btnText=确认, highestPriority=false, customData={presentsInfo=[PresentInfo(presentTitle=预约五五登录礼包, presentDesc=亲密玫瑰x1)]}), code=-1, msg=您的账号存在风险，暂不支持参与该活动, orderId=[])
        """
        # 提取 code 和 msg
        code_msg_match = re.search(r'code=(-?\d+), msg=([^,}\]]+)', log_content)
        if not code_msg_match:

            return None

        code = int(code_msg_match.group(1))
        if code == 0:
            return None
        msg = code_msg_match.group(2).strip()

        # 提取 ShowResult 中的 title 和 desc
        show_result_match = re.search(
            r'ShowResult\(.*?title=([^,]+), desc=([^,]+),', log_content)
        if show_result_match:
            show_title = show_result_match.group(1).strip()
            show_desc = show_result_match.group(2).strip()
        else:
            show_title = None
            show_desc = None

        return {
            'code': code,
            'msg': msg,
            'title': show_title,
            'desc': show_desc
        }
    
    def extract_click_info(self, log_content: str) -> Optional[Dict[str, str]]:
        """
        提取component_name和component_id字段。
        示例日志内容：
        PageReporter_beaconReport]:reportActivityComponentClick Params: {component_name=应用宝领取, component_id=moka-ui-obtain_416a9307, ...}
        """
        match = re.search(
            r'component_name=([^,}]+), component_id=([^,}]+)', log_content)
        if not match:
            return None

        component_name = match.group(1).strip()
        component_id = match.group(2).strip()

        return {
            'component_name': component_name,
            'component_id': component_id
        }

    def format_log_to_download_info(self, filtered_logs: List[str]) -> str:
        return self.format_log_to_info(
            filtered_logs,
            keyword="DownloadInfo",
            extract_func=self.extract_download_info,
            log_desc="下载场景 DownloadInfo"
        )

    def format_log_to_lottery_item_info(self, filtered_logs: List[str]) -> str:
        return self.format_log_to_info(
            filtered_logs,
            keyword="doQueryLotteryResult item",
            extract_func=self.extract_lottery_item_info,
            log_desc="活动页 我的奖品item"
        )

    def format_log_to_obtain_present_info(self, filtered_logs: List[str]) -> str:
        return self.format_log_to_info(
            filtered_logs,
            keyword="doShowResult instance",
            extract_func=self.extract_obtain_present_info,
            log_desc="活动页 奖品领取结果"
        )
    
    def format_log_to_click_info(self, filtered_logs: List[str]) -> str:
        return self.format_log_to_info(
            filtered_logs,
            keyword="reportActivityComponentClick",
            extract_func=self.extract_click_info,
            log_desc="活动页 组件点击上报"
        )


if __name__ == "__main__":

    logs = [
        "2025-02-16 15:27:31.290 I halley-downloader-SectionTransport 1-538A000AF07F75755411BC1DFEF72719:[25|direct] Direct:true send req retCode:-31,msg:javax.net.ssl.SSLHandshakeException:SSL handshake aborted: ssl=0x6ed35f6408: I/O error during system call, Connection reset by peer",
        "2025-02-16 15:27:32.344 I halley-downloader-SectionTransport 1-538A000AF07F75755411BC1DFEF72719:[25|direct] Direct:true send req retCode:-31,msg:javax.net.ssl.SSLHandshakeException:SSL handshake aborted: ssl=0x6e0226e208: I/O error during system call, Connection reset by peer",
        "2025-02-16 15:27:33.395 I halley-downloader-SectionTransport 1-538A000AF07F75755411BC1DFEF72719:[25|direct] Direct:true send req retCode:-31,msg:javax.net.ssl.SSLHandshakeException:SSL handshake aborted: ssl=0x6e0226e388: I/O error during system call, Connection reset by peer",
        "2025-02-16 15:27:31.290 I other-tag 1-538A000AF07F75755411BC1DFEF72719: some other log line with id=12345",
        "2025-02-16 15:27:32.290 I other-tag 1-538A000AF07F75755411BC1DFEF72719: some other log line with id=67890",
        "2025-05-18 14:36:20.759 I CloudGame.CGTracer ###---enterTrace###---end###---CGTracer###",
        "2025-05-05 18:20:33.869 I FLog_TouchSysInterceptor [main]TouchSysInterceptor:PhotonWindowContext#onLoadFinish: triggerInfo=DesktopWinTrigger{triggerAction=1010, popupConfigId=13892, popupScene=10600, popupType=20, showScene=10600}, viewName=desktop_common_right_button_window",
        "2025-05-05 18:20:33.869 I FLog_TouchSysInterceptor #showDesktopWindowReal：, isWallpaperVisible=true, isMa"
    ]

    tag_ignore_patterns = {
        "halley-downloader.*": [
            r"ssl=0x[0-9a-fA-F]+",                          # 忽略ssl内存地址
        ],
        "other-tag": [
            r"id=\d+",                                      # 忽略id数字
        ],
    }

    log_processor = LogProcessor()
    filtered = log_processor.save_log_by_tags_and_content(
            logs,
            save_logs=[('halley-downloader-SectionTransport',''), 
                        ('other-tag', ''),('CloudGame.*', '###---(.*?)###---(.*?)###---(.*?)###(.*?)$'),
                        ('FLog_TouchSysInterceptor', 'PhotonWindowContext#onLoadFinish: triggerInfo='),
                        ('FLog_TouchSysInterceptor', 'showDesktopWindowReal：, isWallpaperVisible=')],
            split_info=None,
            is_fuzzy_match_tag=False,
            dedup_targets=None,
            tag_ignore_patterns=tag_ignore_patterns
        )

    for line in filtered:
        print(line)
