import os
import re
import json
from datetime import datetime, timedelta

from src.modules.log_tool.rag.log_rag import log_rag
from src.common.tools.log_process import LogProcessor
from src.common.logs.logger import app_logger
from src.common.error.detailed_value_error import raise_value_error, ErrorCode



# 1. 查找需要的日志文件
# 1.1 如果是单个日志文件 就直接用该日志文件过滤
# 1.2 如果不是单个日志文件 根据bug时间查找
# 1.3 未根据 bug时间 找到指定文件 -- 最新的两个文件

# 2. 过滤日志
# 2.1 根据tag过滤日志
# 2.2 过滤出的日志是否大于10行 -- 否 -- 继续过滤下一个最新日期的日志 将结果合并。
class LogFilter:
    def __init__(self, logs_path, log_tags, **kwargs):
        self._start_time = None
        self._end_time = None

        # 必填参数
        self._logs_path = logs_path
        self._log_tags = log_tags

        # 可选参数，使用 kwargs.get() 取值，避免可变默认参数陷阱
        self._bug_time = kwargs.get('bug_time', '')
        self._index_keys = kwargs.get('index_keys') or set()
        self._save_logs = kwargs.get('save_logs') or []
        self._delete_logs = kwargs.get('delete_logs') or []
        self._split_info = kwargs.get('split_info') or []
        self._dedup_targets = kwargs.get('dedup_targets') or []
        self._extract_fields = kwargs.get('extract_fields') or {}
        self._is_fuzzy_match_tag = kwargs.get('is_fuzzy_match_tag', False)
        self._is_analyze_daemon = kwargs.get('is_analyze_daemon', False)
        self._tag_ignore_patterns = kwargs.get('tag_ignore_patterns') or {}
        self._placeholder = kwargs.get('placeholder', '')
        self._log_pattern = re.compile(r'_(\d{10})(?:_(\d+))?\.xlog\.log$')
        self._is_analyze_all_logs = kwargs.get('is_analyze_all_logs', False)

        self._format_bug_time()

        # 获取日志文件夹下的所有日志文件列表
        self._log_file_list = []
        self._get_log_file_list()

        # 准备rag数据
        log_rag.prepareLogRag()

        self._index_values = set()
        self._find_index_keys = set()

    def _format_bug_time(self):
        if self._bug_time:
            # 支持多种时间格式的解析
            time_formats = [
                "%Y-%m-%d %H:%M:%S.%f",  # 2025-08-14 17:20:30.000
                "%Y-%m-%d %H:%M:%S",     # 2025-08-14 17:20:30
                "%Y-%m-%d %H:%M",        # 2025-08-14 17:20
            ]

            parsed_time = None
            for fmt in time_formats:
                try:
                    parsed_time = datetime.strptime(self._bug_time, fmt)
                    break
                except ValueError:
                    continue

            if parsed_time is None:
                # 如果所有格式都解析失败，尝试添加默认的秒和毫秒
                if not self._bug_time.endswith('.000'):
                    # 检查是否缺少秒部分
                    if len(self._bug_time.split(' ')) == 2 and len(self._bug_time.split(' ')[1].split(':')) == 2:
                        # 格式是 YYYY-MM-DD HH:MM，添加秒和毫秒
                        self._bug_time += ':00.000'
                    elif not self._bug_time.endswith('.000'):
                        # 格式是 YYYY-MM-DD HH:MM:SS，添加毫秒
                        self._bug_time += '.000'

                try:
                    parsed_time = datetime.strptime(self._bug_time, "%Y-%m-%d %H:%M:%S.%f")
                except ValueError as e:
                    raise ValueError(f"无法解析时间格式: {self._bug_time}，支持的格式: YYYY-MM-DD HH:MM, YYYY-MM-DD HH:MM:SS, YYYY-MM-DD HH:MM:SS.fff") from e

            self._bug_time = parsed_time

    def _get_log_file_list(self):
        if os.path.exists(self._logs_path) and os.path.isdir(self._logs_path):
            self._log_file_list = os.listdir(self._logs_path)
        else:
            self._log_file_list = []

    def __repr__(self):
        # 简单打印部分关键属性，方便调试
        attrs = {
            'logs_path': self._logs_path,
            'log_tags': self._log_tags,
            'bug_time': self._bug_time,
            'index_keys': self._index_keys,
            'save_logs': self._save_logs,
            'delete_logs': self._delete_logs,
            'split_info': self._split_info,
            'dedup_targets': self._dedup_targets,
            'extract_fields': self._extract_fields,
            'is_fuzzy_match_tag': self._is_fuzzy_match_tag,
            'is_analyze_daemon': self._is_analyze_daemon,
            'tag_ignore_patterns': self._tag_ignore_patterns,
            'placeholder': self._placeholder,
            'log_pattern': self._log_pattern,
            'is_analyze_all_logs': self._is_analyze_all_logs
        }
        return f"{self.__class__.__name__}({attrs})"

    def get_filtered_log(self):
        filteredLogs = []
        filteredLogs_daemon = []
        if not self._logs_path:
            app_logger.info(f"未找到日志文件地址: {self._logPath}")
            # raise ValueError(f"未找到日志文件地址: {self._logPath}")
            raise_value_error(ErrorCode.LOG_FILE_PATH_NOT_FOUND, message=f"未找到日志文件地址: {self._logPath}")
        
        log_path = ''
        daemon_log_path = ''
        if os.path.isfile(self._logs_path):
            # 如果是单个日志文件 就直接用该日志文件过滤
            # 单个文件就不根据bug时间过滤了，如果给的时间不对，可能过滤不出日志
            log_path = self._logs_path
        elif os.path.isdir(self._logs_path):
            # 如果不是单个日志文件 根据bug时间查找，无bug时间或找不到，返回最新两个文件
            log_path, daemon_log_path = self.get_log_files()
        
        if log_path or daemon_log_path:
            if log_path:
                filteredLogs, rag, extracted_info = self.filter_log(log_path)
            if daemon_log_path:
                filteredLogs_daemon, rag, extracted_info = self.filter_log(daemon_log_path)
        else:
            # raise ValueError("日志路径为空")
            raise_value_error(ErrorCode.LOG_PATH_IS_EMPTY, message=f"日志路径为空")

        app_logger.info(f'===== 第一次过滤日志 main: ======\n{"".join(filteredLogs)}')

        app_logger.info(f'===== 第一次过滤日志 daemon: ======\n{"".join(filteredLogs_daemon)}')


        # 过滤后的日志是否大于10行
        if ((len(filteredLogs) + len(filteredLogs_daemon)) < 10 or self._is_analyze_all_logs) and os.path.isdir(self._logs_path) and len(self._log_file_list)>1:
            app_logger.info(f'==== 尝试再次过滤 ======')
            # 无视时间，因为有可能在给定的时间范围内，找不到日志。
            self._start_time = None
            self._end_time = None
            log_files = []
            for log_name in self._log_file_list:
                if self._is_analyze_daemon:
                    if ('com.tencent.android.qqdownloader' in log_name  and '@' not in log_name) or 'daemon' in log_name:
                        log_files.append(log_name) 
                else:
                    if 'com.tencent.android.qqdownloader' in log_name and '@' not in log_name:
                        log_files.append(log_name) 
            
            if self._is_analyze_all_logs and len(log_files)>1:
                filteredLogs, filteredLogs_daemon, rag, extracted_info = self._filtered_log_again(log_files, minimum_log_lines=float('inf'))
                
            if not self._is_analyze_all_logs and len(log_files)>1:
                filteredLogs, filteredLogs_daemon, rag, extracted_info = self._filtered_log_again(log_files, minimum_log_lines=10) 
        
        app_logger.info(f'过滤后的日志 main:\n{"".join(filteredLogs)}')
        # app_logger.info(f'过滤后的日志 daemon:\n{"".join(filteredLogs_daemon)}')
        
        return filteredLogs, filteredLogs_daemon, rag, extracted_info

    
    def filter_log(self, log_path):
        # 创建日志过滤实例
        log_processor = LogProcessor()
        log_processor.process(log_path)
        app_logger.info(f'log_path：\n{log_path}')
        filteredLogs = log_processor.filter_log_by_tags_and_time(self._start_time, self._end_time, self._log_tags)

        app_logger.info(f'===== 首次过滤日志 ======\n{"".join(filteredLogs)}')

        if self._delete_logs:
            filteredLogs = log_processor.delete_log_by_tags_and_content(filteredLogs, self._delete_logs, is_fuzzy_match_tag=self._is_fuzzy_match_tag)

        
        extracted_info = {}
        if self._extract_fields:
            for key, method_name in self._extract_fields.items():
                method = getattr(log_processor, method_name, None)
                if callable(method):
                    extracted_info[key] = method(filteredLogs)
                else:
                    extracted_info[key] = None
                    app_logger.warning(f"LogProcessor 没有方法 {method_name}，{key} 设置为 None")


        # RAG 补充相关知识
        for logLine in filteredLogs:
            for indexKey in self._index_keys:
                key = log_processor.findIndexKeyFromLog(indexKey, logLine)
                if key:
                    self._find_index_keys.add(key)
                    key = key.replace(" ", "")
                    key = key.replace("=", ":")
                    self._index_values.add(log_rag.queryExplainByCode(key))
        rag = ""
        for indexValue in self._index_values:
            rag += f"{indexValue}\n"
        app_logger.info("".join(rag))

        # 保存日志，如果没有指定日志，就是所有的self._log_tags的日志
        if not self._save_logs:
            self._save_logs = [(tag, '') for tag in self._log_tags]
            
        filteredLogs = log_processor.save_log_by_tags_and_content(
            filteredLogs,
            save_logs=self._save_logs,
            split_info=self._split_info,
            is_fuzzy_match_tag=self._is_fuzzy_match_tag,
            dedup_targets=self._dedup_targets,
            tag_ignore_patterns=self._tag_ignore_patterns,
            placeholder=self._placeholder
        )

        # 堆栈
        # filteredLogs = log_processor.remove_at_com_content(filteredLogs)

        app_logger.info(f'===== 最后过滤日志 ======\n{"".join(filteredLogs)}')


        return filteredLogs, rag, extracted_info


    # 根据时间来匹配文件名获取日志文件
    def get_log_files(self):
        if not self._log_file_list:
            # raise ValueError(f"日志文件夹 {self._logs_path} 为空，未找到任何日志文件")
            raise_value_error(ErrorCode.LOG_FOLDER_EMPTY_NO_LOG_FILES_FOUND, message=f"日志文件夹 {self._logs_path} 为空，未找到任何日志文件")
    
        log_path = None
        daemon_log_path = None

        app_logger.info(f'time = {self._bug_time}')
        if self._bug_time:
            # 有 bug_time 根据bug时间选取日志文件
            log_path, daemon_log_path = self.find_log_with_time()
        
        # 没有 bug_time 选取最新时间，选择最新日志文件
        if not self._bug_time or not log_path:
            app_logger.info("no time")
            log_path, daemon_log_path = self.find_log_without_time()
        else:
            # 设置起始时间和终止时间，在这里设置，可以确保 bug_time是真实有效的。
            if self._bug_time:
                self._start_time, self._end_time = self._calculate_time_range(self._bug_time)

        app_logger.info(f"找到的主进程日志文件路径：{log_path}")
        app_logger.info(f"找到的daemon日志文件路径：{daemon_log_path}")

        # 返回：主进程日志路径、daemon进程日志路径、所有日志文件名、日志文件夹 路径
        return log_path, daemon_log_path
    
    def find_log_without_time(self):
        log_files = []
        for file in self._log_file_list:
            if '@' not in file:
                log_files.append(file)
        # 将日志文件按时间排序
        log_files = self.sort_logs_with_time(log_files)
        app_logger.info(f'log_files = {log_files}')
        # 获取两个最新时间戳的日志文件，如果只有一个文件，就返回该文件
        files = log_files[-2:]
        app_logger.info(f'files = {files}')
        log_path, daemon_log_path = self.get_final_filename(files) 
        # 返回：主进程日志路径、daemon进程日志路径
        return log_path, daemon_log_path
    
    def find_log_with_time(self):
        # 判断是否 相邻整点 相差在10分钟内，得到相邻整点字符串
        near_clock_str = self.is_within_ten_minutes()
        app_logger.info(f'near_clock_str= {near_clock_str}')
       
        # 解析原始时间字符串为datetime对象
        # datetime_obj = datetime.strptime(self._bug_time, "%Y-%m-%d %H:%M:%S.%f")
        # 转换为目标格式的字符串
        target_time_str = self._bug_time.strftime("%Y%m%d%H")
        app_logger.info(f"用户输入bug时间：{target_time_str}")
        
        log_files = []
        for file in self._log_file_list:
            if '@' not in file and target_time_str in file:
                log_files.append(file)
            elif near_clock_str and '@' not in file and near_clock_str in file:
                log_files.append(file)
        log_files = self.sort_logs_with_time(log_files)
        app_logger.info(f"要合并的日志路径：{log_files}")


        log_path = None
        daemon_log_path = None
        log_path, daemon_log_path = self.get_final_filename(log_files)
    
        # 返回：主进程日志路径、daemon进程日志路径
        return log_path, daemon_log_path
    
    def is_within_ten_minutes(self):        
        # 获取当前时间的小时
        current_hour = self._bug_time.hour
        
        # 计算上一个整点和下一个整点的小时
        previous_hour = current_hour - 1 if current_hour > 0 else 23
        next_hour = current_hour + 1 if current_hour < 23 else 0
        
        # 创建上一个整点的 datetime 对象
        previous_time = self._bug_time.replace(hour=previous_hour, minute=0, second=0, microsecond=0)
        # 如果 previous_hour 是 23 且 current_hour 是 0，说明 previous_time 是前一天的23点，需要减一天
        if current_hour == 0 and previous_hour == 23:
            previous_time -= timedelta(days=1)
        
        # 创建下一个整点的 datetime 对象
        next_time = self._bug_time.replace(hour=next_hour, minute=0, second=0, microsecond=0)
        # 如果 next_hour 是 0 且 current_hour 是 23，说明 next_time 是第二天的0点，需要加一天
        if current_hour == 23 and next_hour == 0:
            next_time += timedelta(days=1)
        
        # 检查与上一个整点的间隔
        if (self._bug_time - previous_time) <= timedelta(minutes=10):
            return previous_time.strftime("%Y%m%d%H")
        
        # 检查与下一个整点的间隔
        if (next_time - self._bug_time) <= timedelta(minutes=10):
            return next_time.strftime("%Y%m%d%H")
        
        return None
        

    def sort_logs_with_time(self, logs, is_reverse=False):
        def extract_key(log):
            match = self._log_pattern.search(log)
            if not match:
                return ('0000000000', 0)
            timestamp = match.group(1)
            suffix_str = match.group(2)
            try:
                suffix = int(suffix_str) if suffix_str else 0
            except ValueError:
                suffix = 0
            return (timestamp, suffix)
        return sorted(logs, key=extract_key, reverse=is_reverse)
    
    def get_final_filename(self, files):
        log_path = ''
        daemon_log_path = ''
        if files:
            if len(files) > 1:
                # 合并两个文件
                file_to_merge = []
                file_to_merge.append(os.path.join(self._logs_path, files[0]))
                file_to_merge.append(os.path.join(self._logs_path, files[1]))
                app_logger.info(f'file_to_merge = {file_to_merge}')
                log_path = self._merge_logs(file_to_merge)
            else:
                # 获取一个最新日期文件的路径
                log_path = os.path.join(self._logs_path, files[0])
            
            # daemon进程
            if self._is_analyze_daemon:
                # 选中的daemon文件名 列表
                daemon_files = self.get_daemon_files(files)
                if daemon_files:
                    if len(daemon_files) > 1:
                        # 合并两个文件
                        file_to_merge = []
                        file_to_merge.append(os.path.join(self._logs_path, daemon_files[0]))
                        file_to_merge.append(os.path.join(self._logs_path, daemon_files[1]))
                        app_logger.info(f'file_to_merge = {file_to_merge}')
                        daemon_log_path = self._merge_logs(file_to_merge)
                    else:
                        # 获取一个最新日期文件的路径
                        daemon_log_path = os.path.join(self._logs_path, daemon_files[0])
        return log_path, daemon_log_path
    
    def get_daemon_files(self, file):
        # 选中的daemon文件名 列表
        app_logger.info(f'== get_daemon_files ==\n主进程日志文件： {file}')
        daemon_files = []
        for file_name in file:
            daemon_file = self.add_log_filename_with_daemon(file_name)
            if daemon_file in self._log_file_list:
                daemon_files.append(daemon_file)
        app_logger.info(f'== get_daemon_files ==\ndaemon日志文件： {daemon_files}')
        return daemon_files
    
    def add_log_filename_with_daemon(self, filename: str) -> str:
        """
        将形如 'com.tencent.android.qqdownloader_2025032615.xlog.log' 的文件名
        转换成 'com.tencent.android.qqdownloader@daemon_2025032615.xlog.log'。

        规则：
        - 找到第一个下划线，将其替换为 '@daemon_'。
        - 其余部分保持不变。

        :param filename: 原始文件名字符串
        :return: 转换后的文件名字符串
        """
        # 找到第一个下划线的位置
        idx = filename.find('_')
        if idx == -1:
            # 如果没有下划线，返回原字符串
            return filename

        # 在第一个下划线处插入 '@daemon'
        return filename[:idx] + '@daemon' + filename[idx:]

    def _filtered_log_again(self, log_files, minimum_log_lines):
        logs = self.sort_logs_with_time(log_files, is_reverse=True)
        app_logger.info(f'filtered_log_again === logs: {logs}')
        
        filteredLogs = []
        filteredLogs_daemon = []
        combined_extracted_info = {}  # 用于合并所有日志的 extracted_info
        rag = ""  # 用于合并rag

        for log_name in logs:
            new_logs = []
            new_daemon_logs = []
            app_logger.info(f'filtered_log_again === log_name: {log_name}')
            if 'daemon' in log_name:
                new_daemon_logs, new_rag, new_extracted_info = self.filter_log(os.path.join(self._logs_path, log_name))
            else:
                new_logs, new_rag, new_extracted_info = self.filter_log(os.path.join(self._logs_path, log_name))
            
            # 合并 extracted_info
            for key, value in new_extracted_info.items():
                # value不是列表 是[]拼接起来的字符串
                if value != "[]":  # 新的 value 非空才处理
                    if key not in combined_extracted_info:
                        combined_extracted_info[key] = value
                    else:
                        # 旧的 value 非空才添加
                        if combined_extracted_info[key]:
                            json1 = json.loads(value)
                            json2 = json.loads(combined_extracted_info[key])
                            merged_str = json.dumps(json1 + json2, ensure_ascii=False, indent=2)
                            combined_extracted_info[key] = merged_str
                        else:
                            combined_extracted_info[key] = value
            
            # rag 合并，不重复
            if new_rag and new_rag not in rag:
                rag += new_rag
            
            # 把 new_logs 放到 filteredLogs 前面，new_logs的时间更早
            filteredLogs = new_logs + filteredLogs
            filteredLogs_daemon = new_daemon_logs + filteredLogs_daemon

            if (len(filteredLogs) + len(filteredLogs_daemon)) > minimum_log_lines:
                break

        return filteredLogs, filteredLogs_daemon, rag, combined_extracted_info

    def _merge_logs(self, log_files):
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S-%f")
        merged_log_path = os.path.join(self._logs_path, f'merged_log_{current_time}.log')
        with open(merged_log_path, 'wb') as merged_file:
            for log_file in log_files:
                app_logger.info(f'正在合并 {log_file}')
                with open(log_file, 'rb') as f:
                    merged_file.write(f.read())
                    merged_file.write(b'\n')
        app_logger.info(f"合并日志文件成功，保存到: {merged_log_path}")
        return merged_log_path
    

    def _calculate_time_range(self, time_str: str) -> tuple[datetime, datetime]:
        # 计算时间偏移量
        delta_backward = timedelta(minutes=20)  # 前推20分钟
        delta_forward = timedelta(minutes=10)  # 后延10分钟

        # 生成结果时间
        past_time = self._bug_time - delta_backward
        future_time = self._bug_time + delta_forward

        return past_time, future_time



if __name__ == "__main__":
    # # 设置日志文件路径
    # logs_path = '/data/workspace/logassistant-server/data/TDOSLog_20250419_000553462_21752_58453'
    # # 设置过滤标签
    # log_tags = ['PageReporter_beaconReport', 'HttpProtocolInterceptor', 'HttpRequest',
    #                       'HTTPRequester', 'RuntimeCrash',
    #                       'ReceivingRewardViewModel', 'YybActCommonReceiveManager',
    #                       'YybLotteryView','YybLotteryViewModel'] 
    
    # # 保存关键日志，连续重复的日志，只保存第一条。
    # save_logs = [
    #     ('ReceivingRewardViewModel', 'orderStatus=3'),
    #     ('YybActCommonReceiveManager', 'doShowResult instance'),
    #     ('PageReporter_beaconReport', 'reportActivityComponentClick'),
    #     ('YybLotteryView', ''),
    #     ('YybLotteryViewModel', '')
    # ]
    # # 将日志行中无用的部分截除
    # split_info = [
    #     ('ReceivingRewardViewModel', 'img'),
    #     ('YybActCommonReceiveManager', 'propertyData')
    # ]
    # # 将无用的重复日志删除
    # dedup_targets = [
    #     ('ReceivingRewardViewModel', 'doQueryLotteryResult item')
    # ]

    # extract_fields = {
    #             "lottery_item_info": "format_log_to_lottery_item_info",
    #             "obtain_present_info": "format_log_to_obtain_present_info",
    #             "click_info": "format_log_to_click_info"
    #         }

    # bug_time = '2025-04-17 13:59:28'  
    # bug_time = None
    # bug_time = '2025-04-18 23:49:25.000'  
    bug_time = '2025-04-18 23:45:25.000'

    logs_path = '/Users/<USER>/Desktop/日志分析/其他/TDOSLog_20250526_113952683_14866_67456'
    # log_tags = ['AliveFreezeCheckTask', 'TimerClean.*']
    # log_tags = ['StatusBarEventController', 'what=\d+']
    log_tags = ['exp_banner_video_fx_card/HomePageOverlayFxCard']
    # save_logs=[('FLog_TouchSysInterceptor', 'PhotonWindowContext#onLoadFinish: triggerInfo='),
    #             ('FLog_TouchSysInterceptor', '--ReportShowOrClickEngine----:'),
    #             ('FLog_TouchSysInterceptor', 'showDesktopWindowReal：, isWallpaperVisible=')]
    save_logs = []
    split_info = []
    # delete_logs = [('TimerCleanManager', 'finish.*')]
    delete_logs = []
    dedup_targets = []
    extract_fields = {}

    # 创建 LogFilter 实例
    log_filter = LogFilter(
        logs_path=logs_path,
        log_tags=log_tags,
        bug_time=bug_time,
        save_logs=save_logs, 
        delete_logs=delete_logs,
        split_info=split_info, 
        dedup_targets = dedup_targets, 
        extract_fields=extract_fields,
        is_fuzzy_match_tag=True,
        is_analyze_daemon=True
    )

    # 获取过滤后的日志
    filtered_logs, filtered_logs_deamon, rag, extracted_info = log_filter.get_filtered_log()
