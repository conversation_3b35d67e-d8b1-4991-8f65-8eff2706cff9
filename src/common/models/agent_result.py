from dataclasses import dataclass
from typing import Optional


@dataclass
class AgentResult:
    """Module模块功能统一返回格式"""
    content: str  # 返回内容（结果文件路径或普通文本）
    result_type: str = "success"  # 结果类型: "success", "error"
    error_message: Optional[str] = None  # 错误信息
    
    @classmethod
    def success(cls, content: str) -> 'AgentResult':
        """创建成功结果"""
        return cls(
            content=content,
            result_type="success"
        )
    
    @classmethod
    def error(cls, error_message: str) -> 'AgentResult':
        """创建错误结果"""
        return cls(
            content=error_message,
            result_type="error",
            error_message=error_message
        ) 