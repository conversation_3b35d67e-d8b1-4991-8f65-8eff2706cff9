from dataclasses import dataclass
from typing import Any, Optional, List, Dict


@dataclass
class BusinessResult:
    """业务处理结果统一返回格式"""
    success: bool
    data: Any = None
    error: str = None
    with_buttons: bool = False  # 是否需要添加按钮
    callback_id: Optional[str] = None  # 按钮回调ID
    button_actions: Optional[List[Dict[str, Any]]] = None  # 按钮配置 
    query_text: Optional[str] = None  # 原始子问题文本

    @classmethod
    def success(cls, data: Any = None, with_buttons: bool = False, 
                callback_id: Optional[str] = None, 
                button_actions: Optional[List[Dict[str, Any]]] = None,
                query_text: Optional[str] = None) -> 'BusinessResult':
        """创建成功结果"""
        return cls(
            success=True,
            data=data,
            with_buttons=with_buttons,
            callback_id=callback_id,
            button_actions=button_actions,
            query_text=query_text
        )
    
    @classmethod
    def fail(cls, error_message: str) -> 'BusinessResult':
        """创建错误结果"""
        return cls(
            success=False,
            error=error_message
        ) 