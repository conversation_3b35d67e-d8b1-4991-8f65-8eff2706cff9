from dataclasses import dataclass
from typing import Dict, Any, Optional

"""slot_filter 解析结果"""
@dataclass
class SlotFilterResult:
    """SlotFilter解析结果"""
    is_require_more_info: str  # 大模型判断是否需要更多信息
    follow_up_prompt: str  # 大模型生成的追问提示
    extracted_params: Dict[str, Any]  # 提取的参数

    def __post_init__(self):
        self.extracted_params = self._none_to_none(self.extracted_params)

    @staticmethod
    def _none_to_none(obj):
        """将none转换为None"""
        if isinstance(obj, dict):
            return {k: SlotFilterResult._none_to_none(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [SlotFilterResult._none_to_none(v) for v in obj]
        elif obj == "none":
            return None
        else:
            return obj 