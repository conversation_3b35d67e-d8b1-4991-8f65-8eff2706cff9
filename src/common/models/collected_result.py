from dataclasses import dataclass
from typing import Optional, List, Dict, Any


@dataclass
class CollectedResult:
    """收集的结果统一格式"""
    type: str  # 'text' 或 'file'
    content: Optional[str] = None  # 文本内容（type='text'时使用）
    file_path: Optional[str] = None  # 文件路径（type='file'时使用）
    user_input: str = "❔"  # 用户输入/子问题文本
    with_buttons: bool = False  # 是否包含按钮
    button_actions: Optional[List[Dict[str, Any]]] = None  # 按钮配置
    callback_id: Optional[str] = None  # 按钮回调ID
    
    @classmethod
    def text_result(cls, content: str, user_input: str = "❔", 
                   with_buttons: bool = False, 
                   button_actions: Optional[List[Dict[str, Any]]] = None,
                   callback_id: Optional[str] = None) -> 'CollectedResult':
        """创建文本结果"""
        return cls(
            type='text',
            content=content,
            user_input=user_input,
            with_buttons=with_buttons,
            button_actions=button_actions,
            callback_id=callback_id
        )
    
    @classmethod
    def file_result(cls, file_path: str, user_input: str = "❔") -> 'CollectedResult':
        """创建文件结果"""
        return cls(
            type='file',
            file_path=file_path,
            user_input=user_input
        )
    
    def is_file(self) -> bool:
        """判断是否为文件类型"""
        return self.type == 'file'
    
    def is_text(self) -> bool:
        """判断是否为文本类型"""
        return self.type == 'text' 