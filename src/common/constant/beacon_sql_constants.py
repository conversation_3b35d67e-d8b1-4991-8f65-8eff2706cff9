"""
Beacon API SQL 常量定义文件
包含所有用于 Beacon API 查询的 SQL 语句模板
"""


class BeaconSQLConstants:
    """Beacon API SQL 语句常量类"""
    
    # 数据源ID常量
    DATA_SOURCE_ID = 1235751
    AD_DATA_SOURCE_ID = 1420647
    
    # 联网用户数查询 SQL
    ONLINE_USER_COUNT_SQL = """
        SELECT ds, qua, count(DISTINCT guid)
        FROM beacon_olap.dws_ydc_networking_guid_di
        WHERE ds >= {start_date_formatted}
            AND ds <= {end_date_formatted}
            AND qua IN ({qua_conditions})
        GROUP BY ds, qua
        ORDER BY ds, qua
        LIMIT 5000
        """
    
    # 启动速度分析 SQL
    LAUNCH_SPEED_ANALYTICS_SQL = """
        SELECT qua, launch_type, start_type, run_type, crab_shell_type,
          case
            when crab_shell_type = 2 and launch_type=1 and start_type=2 and run_type=1 then '应用首次断层冷启动'
            when crab_shell_type = 2 and launch_type=1 and start_type=2 and run_type=3 then '当前版本首次断层冷启动'
            when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=1 then '应用首次外call冷启动'
            when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=3 then '当前版本首次外call冷启动'
            when crab_shell_type = 2 and launch_type=1 and start_type=1 and run_type=2 then '常规冷启动'
            when crab_shell_type = 2 and launch_type=1 and start_type=3 and run_type=2 then '常规外call冷启动'
            when crab_shell_type = 2 and launch_type=3 and start_type=1 and run_type=2 then '常规热启动'
            when crab_shell_type = 2 and launch_type=3 and start_type=2 and run_type=1 then '首次断层热启动'
            when crab_shell_type = 2 and launch_type=3 and start_type=3 and run_type=1 then '首次外call热启动'
            when crab_shell_type = 2 and launch_type=3 and start_type=3 and run_type=2 then '常规外call热启动'

            when crab_shell_type = 1 and launch_type=1 and start_type=2 and run_type=1 then '套壳应用首次断层冷启动'
            when crab_shell_type = 1 and launch_type=1 and start_type=2 and run_type=3 then '套壳当前版本首次断层冷启动'
            when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=1 then '套壳应用首次外call冷启动'
            when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=3 then '套壳当前版本首次外call冷启动'
            when crab_shell_type = 1 and launch_type=1 and start_type=1 and run_type=2 then '套壳常规冷启动'
            when crab_shell_type = 1 and launch_type=1 and start_type=3 and run_type=2 then '套壳常规外call冷启动'
            when crab_shell_type = 1 and launch_type=3 and start_type=1 and run_type=2 then '套壳常规热启动'
            when crab_shell_type = 1 and launch_type=3 and start_type=2 and run_type=1 then '套壳首次断层热启动'
            when crab_shell_type = 1 and launch_type=3 and start_type=3 and run_type=1 then '套壳首次外call热启动'
            when crab_shell_type = 1 and launch_type=3 and start_type=3 and run_type=2 then '套壳常规外call热启动'
          else '其他' end as desc_str,
        COUNT(*) as 次数,
        AVG(tag_duration) as 平均值,
        ApproxPercentile(tag_duration, 0.5) as 50分位,
        ApproxPercentile(tag_duration, 0.8) as 80分位,
        ApproxPercentile(tag_duration, 0.9) as 90分位
        FROM [1055717].[launch_speed_event]
        WHERE ds >= {start_date_formatted}
          AND ds <= {end_date_formatted}
          AND tag = "Draw_End"
          AND tagger_id = 1
          AND qua IN ({qua_conditions})
          AND content_type not LIKE '%ANCHOR_GAME_TAB%'
          AND content_type not LIKE '%SPLASH_IMAGE%'
          AND content_type not LIKE '%MAIN_SPLASH_VIEW%'
        GROUP BY qua, launch_type, start_type, run_type, crab_shell_type
        ORDER BY desc_str asc, qua asc
        LIMIT 10000
        """
    
    # 联网覆盖率数据查询 SQL
    NETWORK_COVERAGE_DATA_SQL = """
        SELECT ds, count(DISTINCT guid) as total_networking_users
        FROM beacon_olap.dws_ydc_networking_guid_di
        WHERE ds = {date_formatted}
        GROUP BY ds
        LIMIT 1
        """

    # 外call下载率查询 SQL
    EXTERNAL_CALL_DOWNLOAD_RATE_SQL = """
        select
             ds as '日期',
             qua as '版本',
             continue_uv as '外call承接户数',
             outcall_uv as '外call曝光户数',
             concat(cast(cast(outcall_uv*100/continue_uv as decimal(10,2)) as string),'%') as '外call卡曝光率',
             continue_pv as '外call承接曝光pv',
             outcall_pv as '外call曝光pv',
             start_dload_uv as '开始下载户数',
             concat(cast(cast(start_dload_uv*100/outcall_uv as decimal(10,2)) as string),'%') as '外call开始下载率',
             succ_dload_uv as '成功下载户数',
             concat(cast(cast(succ_dload_uv*100/start_dload_uv as decimal(10,2)) as string),'%') as '外call成功下载率',
             start_dload_cnt as '开始下载量',
             succ_dload_cnt as '成功下载量',
             fail_dload_cnt as '失败下载量',
             pause_dload_cnt as '暂停下载量',
             fail_dload_uv as '失败下载户数',
             pause_dload_uv as '暂停下载户数'
        from(
        select
             t1.ds as ds,
             t1.qua as qua,
             count(distinct t1.guid) as uv,
             count(distinct if(continue_pv>0,t1.guid,null)) as continue_uv,
             count(distinct if(outcall_pv>0,t1.guid,null)) as outcall_uv,
             sum(continue_pv) as continue_pv,
             sum(outcall_pv)as outcall_pv,
             count(distinct if(start_dload_cnt>0,t1.guid,null)) as start_dload_uv,
             count(distinct if(succ_dload_cnt>0,t1.guid,null)) as succ_dload_uv,
             sum(start_dload_cnt) as start_dload_cnt,
             sum(succ_dload_cnt) as succ_dload_cnt,
             sum(fail_dload_cnt) as fail_dload_cnt,
             sum(pause_dload_cnt) as pause_dload_cnt,
             count(distinct if(fail_dload_cnt>0,t1.guid,null)) as fail_dload_uv,
             count(distinct if(pause_dload_cnt>0,t1.guid,null)) as pause_dload_uv
        from(
        select
            distinct ds,guid,qua,user_type,out_caller_type,out_caller_style
        from beacon_olap.t_md_soft_yyb_outcaller_act_pv_dload_guid
        where ds >= {start_date_formatted} and ds <= {end_date_formatted}
              and  qua IN ({qua_conditions})
        )t1
        left join(
        select
            cast(substr(cast(ds as string),1,8) as bigint) as ds,
            guid,
            sum(case  when action_id=100 and scene='10058' then 1 else 0 end) as continue_pv,
            sum(case  when action_id=100 and caller in(2,3,12,13,20,21,27,28,29,52,100) and scene in('20100216','20100217','30142','30195','10037','10038') then 1 else 0 end)as outcall_pv,
            sum(case  when  event_code='AppStartDownload'
                            and (
                                         (scene in ('30142','30195') AND small_scene IN ('06','-1','22'))
                                      or  scene in ('10037','10038')
                                      OR (scene in ('20100216','20100217') AND small_scene IN ('06','-1'))
                                     ) then 1 else 0 end) as start_dload_cnt,
            sum(case  when event_code='AppSuccDownload'
                           and (
                                (   scene in ('30142','30195') AND small_scene IN ('06','-1','22'))
                                or  scene in ('10037','10038')
                                OR (scene in ('20100216','20100217') AND small_scene IN ('06','-1'))
                               ) then 1 else 0 end) as succ_dload_cnt,
            sum(case  when event_code='AppFailDownload'
                           and (
                                (   scene in ('30142','30195') AND small_scene IN ('06','-1','22'))
                                or  scene in ('10037','10038')
                                OR (scene in ('20100216','20100217') AND small_scene IN ('06','-1'))
                               ) then 1 else 0 end) as fail_dload_cnt,
            sum(case  when event_code='AppPauseDownload'
                           and (
                                (   scene in ('30142','30195') AND small_scene IN ('06','-1','22'))
                                or  scene in ('10037','10038')
                                OR (scene in ('20100216','20100217') AND small_scene IN ('06','-1'))
                               ) then 1 else 0 end) as pause_dload_cnt
        from
           beacon_olap.dwd_yyb_event_log_beacon_hi
        where
            ds >= {start_date_hour_formatted} and ds <= {end_date_hour_formatted}
                and (scene='10058' or caller in(2,3,12,13,20,21,27,28,29,52,100))
                and length(guid) >=2
                and guid rlike '^\\\\d+$'
        group by cast(substr(cast(ds as string),1,8) as bigint),guid
        )t2
        on t1.ds=t2.ds and t1.guid=t2.guid
        group by
             t1.ds
             ,t1.qua
        )ttt
        order by ds asc,qua desc
        LIMIT 10000
        """

    # 云游插件拉起成功率查询 SQL
    CLOUD_GAMING_PLUGIN_LAUNCH_SUCCESS_RATE_SQL = """
        select
          substring(event_time, 1, 10) as dim_0,
          app_version as dim_1,
          NDV(
            CASE
              WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
              AND (
                (
                  stage_name IN ('pip_point_uri_call')
                  AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
                )
              ) THEN uin
            END
          ) as tamst跳转用户数,
          NDV(
            CASE
              WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
              AND (
                (
                  stage_name IN ('pip_point_open_activity')
                  AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
                )
              ) THEN uin
            END
          ) as openActivity用户数,
          NDV(
            CASE
              WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
              AND (
                (
                  stage_name IN ('pip_point_open_activity_start')
                  AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
                )
              ) THEN uin
            END
          ) as `openActivity-实际启动用户数`,(
            NDV(
              CASE
                WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
                AND (
                  (
                    stage_name IN ('pip_point_open_activity')
                    AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
                  )
                ) THEN uin
              END
            )
          ) /(
            NDV(
              CASE
                WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
                AND (
                  (
                    stage_name IN ('pip_point_uri_call')
                    AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
                  )
                ) THEN uin
              END
            )
          ) as 端内插件点击到拉起率,(
            NDV(
              CASE
                WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
                AND (
                  (
                    stage_name IN ('pip_point_open_activity_start')
                    AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
                  )
                ) THEN uin
              END
            )
          ) /(
            NDV(
              CASE
                WHEN CAST(event_code AS STRING) = 'plugin_pip_cost_event'
                AND (
                  (
                    stage_name IN ('pip_point_open_activity')
                    AND plugin_name IN ('com.tencent.assistant.plugin.cloudgame')
                  )
                ) THEN uin
              END
            )
          ) as 端内插件点击到实际执行率
        from
          (
            SELECT
              app_version AS app_version,
              event_code AS event_code,
              c03 AS stage_name,
              uin AS uin,
              c01 AS plugin_name,
              event_time AS event_time
            FROM
              beacon_olap.t_od_light_olap_0m300etnja170g1m
            WHERE
              (
                (
                  (
                    (
                      (
                        ((ds >= {start_date_hour_formatted}))
                        AND ((ds < {end_date_hour_formatted_plus_one}))
                      )
                    )
                    AND (
                      (
                        (event_time >= '{start_date_time}')
                        AND (event_time < '{end_date_time_plus_one}')
                      )
                    )
                  )
                )
                AND (((event_code = 'plugin_pip_cost_event')))
                AND (
                  (
                    (
                      (
                        c01 IN ('com.tencent.assistant.plugin.cloudgame')
                        AND app_version IN ({app_version_conditions})
                      )
                    )
                  )
                )
                AND ((product_id = '0M300ETNJA170G1M'))
              )
          ) t
        group by
          dim_0,
          dim_1
        order by
          dim_0 desc
        limit
          5000
        """

    # 弹窗成功率查询 SQL
    POPUP_SUCCESS_RATE_SQL = """
        SELECT ds AS '时间' ,
            qua AS '版本号',
            pop_times AS '弹窗人数' ,
            expose_times AS '曝光人数',
            CASE
                WHEN cast(round((expose_times / pop_times) * 100,2) AS float) <= 100 THEN cast(round((expose_times / pop_times) * 100,2) AS float)
                ELSE 100
            END AS '弹窗成功率(%)'
        FROM
        (SELECT t1.ds,
                qua,
                count(DISTINCT if(event_code = 'st_pop_action_request' AND uni_start_abort IN (0), t1.guid,NULL)) AS pop_times,
                count(DISTINCT if(event_code = 'st_pop_action_response' , t1.guid,NULL)) AS expose_times
        FROM
            (SELECT substring(cast(ds AS string), 1, 8) AS ds,
                    event_code AS event_code,
                    CASE WHEN (event_code = 'st_pop_action_response') THEN C23 WHEN (event_code = 'st_pop_action_request') THEN C17 ELSE NULL END AS guid,
                    CASE WHEN (event_code = 'st_pop_action_response') THEN C24 WHEN (event_code = 'st_pop_action_request') THEN C18 ELSE NULL END AS qua,
                    CASE WHEN (event_code = 'st_pop_action_response') THEN NULL WHEN (event_code = 'st_pop_action_request') THEN CAST(C03 AS BIGINT) ELSE NULL END AS uni_start_abort
            FROM beacon_olap.t_od_light_olap_0m300etnja170g1m
            WHERE cast(substr(cast(ds as string),1,8) as bigint) BETWEEN {start_date_formatted} AND {end_date_formatted}
                AND ((event_code = 'st_pop_action_response' AND C24 IN ({qua_conditions})) OR (event_code = 'st_pop_action_request' AND C18 IN ({qua_conditions})))
                AND product_id = '0M300ETNJA170G1M' ) t1 
        JOIN
        (SELECT ds,guid,user_type
        FROM beacon_olap.dws_yyb_dau_di
        WHERE ds BETWEEN {start_date_formatted} AND {end_date_formatted}
        AND user_type IN ({user_type_conditions})
        GROUP BY ds,guid,user_type)t2 ON t1.guid = t2.guid and cast(t1.ds as bigint) = t2.ds
        GROUP BY 1,2)t
        order by 1
        """
    
    @classmethod
    def get_online_user_count_sql(cls, start_date_formatted: str, end_date_formatted: str, qua_conditions: str) -> str:
        """
        获取联网用户数查询 SQL
        
        :param start_date_formatted: 格式化的开始日期 (YYYYMMDD)
        :param end_date_formatted: 格式化的结束日期 (YYYYMMDD)
        :param qua_conditions: QUA 条件字符串
        :return: 格式化的 SQL 语句
        """
        return cls.ONLINE_USER_COUNT_SQL.format(
            start_date_formatted=start_date_formatted,
            end_date_formatted=end_date_formatted,
            qua_conditions=qua_conditions
        )
    
    @classmethod
    def get_launch_speed_analytics_sql(cls, start_date_formatted: str, end_date_formatted: str, qua_conditions: str) -> str:
        """
        获取启动速度分析 SQL
        
        :param start_date_formatted: 格式化的开始日期 (YYYYMMDDHH)
        :param end_date_formatted: 格式化的结束日期 (YYYYMMDDHH)
        :param qua_conditions: QUA 条件字符串
        :return: 格式化的 SQL 语句
        """
        return cls.LAUNCH_SPEED_ANALYTICS_SQL.format(
            start_date_formatted=start_date_formatted,
            end_date_formatted=end_date_formatted,
            qua_conditions=qua_conditions
        )
    
    @classmethod
    def get_network_coverage_data_sql(cls, date_formatted: str) -> str:
        """
        获取联网覆盖率数据查询 SQL

        :param date_formatted: 格式化的日期 (YYYYMMDD)
        :return: 格式化的 SQL 语句
        """
        return cls.NETWORK_COVERAGE_DATA_SQL.format(
            date_formatted=date_formatted
        )

    @classmethod
    def get_external_call_download_rate_sql(cls, start_date_formatted: str, end_date_formatted: str,
                                          start_date_hour_formatted: str, end_date_hour_formatted: str,
                                          qua_conditions: str) -> str:
        """
        获取外call下载率查询 SQL

        :param start_date_formatted: 格式化的开始日期 (YYYYMMDD)
        :param end_date_formatted: 格式化的结束日期 (YYYYMMDD)
        :param start_date_hour_formatted: 格式化的开始日期时间 (YYYYMMDDHH)
        :param end_date_hour_formatted: 格式化的结束日期时间 (YYYYMMDDHH)
        :param qua_conditions: QUA 条件字符串
        :return: 格式化的 SQL 语句
        """
        return cls.EXTERNAL_CALL_DOWNLOAD_RATE_SQL.format(
            start_date_formatted=start_date_formatted,
            end_date_formatted=end_date_formatted,
            start_date_hour_formatted=start_date_hour_formatted,
            end_date_hour_formatted=end_date_hour_formatted,
            qua_conditions=qua_conditions
        )

    @classmethod
    def get_cloud_gaming_plugin_launch_success_rate_sql(cls, start_date_hour_formatted: str,
                                                       end_date_hour_formatted_plus_one: str,
                                                       start_date_time: str,
                                                       end_date_time_plus_one: str,
                                                       app_version_conditions: str) -> str:
        """
        获取云游插件拉起成功率查询 SQL

        :param start_date_hour_formatted: 格式化的开始日期时间 (YYYYMMDDHH)
        :param end_date_hour_formatted_plus_one: 格式化的结束日期时间+1 (YYYYMMDDHH)
        :param start_date_time: 开始时间字符串 (YYYY-MM-DD HH:MM:SS)
        :param end_date_time_plus_one: 结束时间字符串+1 (YYYY-MM-DD HH:MM:SS)
        :param app_version_conditions: app_version 条件字符串
        :return: 格式化的 SQL 语句
        """
        return cls.CLOUD_GAMING_PLUGIN_LAUNCH_SUCCESS_RATE_SQL.format(
            start_date_hour_formatted=start_date_hour_formatted,
            end_date_hour_formatted_plus_one=end_date_hour_formatted_plus_one,
            start_date_time=start_date_time,
            end_date_time_plus_one=end_date_time_plus_one,
            app_version_conditions=app_version_conditions
        )

    @classmethod
    def get_popup_success_rate_sql(cls, start_date_formatted: str, end_date_formatted: str,
                                 qua_conditions: str, user_type_conditions: str) -> str:
        """
        获取弹窗成功率查询 SQL

        :param start_date_formatted: 格式化的开始日期 (YYYYMMDD)
        :param end_date_formatted: 格式化的结束日期 (YYYYMMDD)
        :param qua_conditions: QUA 条件字符串
        :param user_type_conditions: 用户类型条件字符串
        :return: 格式化的 SQL 语句
        """
        return cls.POPUP_SUCCESS_RATE_SQL.format(
            start_date_formatted=start_date_formatted,
            end_date_formatted=end_date_formatted,
            qua_conditions=qua_conditions,
            user_type_conditions=user_type_conditions
        )

    @classmethod
    def get_advertisement_data_sql(cls, start_date: str, end_date: str,
                                 version_conditions: str, is_scene: int = 0) -> str:
        """
        获取广告数据查询 SQL

        :param start_date: 开始日期 (YYYYMMDD)
        :param end_date: 结束日期 (YYYYMMDD)
        :param version_conditions: 版本条件字符串
        :param is_scene: 是否按场景分组，1=按场景分组，0=不分组
        :return: 格式化的 SQL 语句
        """
        return cls.ADVERTISEMENT_DATA_SQL.format(
            start_date=start_date,
            end_date=end_date,
            version_conditions=version_conditions,
            is_scene=is_scene
        )

    # 广告数据查询 SQL
    ADVERTISEMENT_DATA_SQL = """
        select imp_date,
          yyb_version,
          scene,
          sum(coalesce(max_exp_cnt, 0)) as total_exp_cnt,
          sum(coalesce(max_click_cnt, 0)) as total_click_cnt,
          sum(coalesce(max_dload_cnt, 0)) as total_dload_cnt,
          sum(coalesce(max_install_cnt, 0)) as total_install_cnt,
          case when sum(coalesce(max_exp_cnt, 0)) > 0 then
            round(sum(coalesce(max_click_cnt, 0)) * 100.0 / sum(coalesce(max_exp_cnt, 0)), 2)
            else 0 end as click_exposure_rate,
          case when sum(coalesce(max_click_cnt, 0)) > 0 then
            round(sum(coalesce(max_dload_cnt, 0)) * 100.0 / sum(coalesce(max_click_cnt, 0)), 2)
            else 0 end as download_click_rate,
          case when sum(coalesce(max_dload_cnt, 0)) > 0 then
            round(sum(coalesce(max_install_cnt, 0)) * 100.0 / sum(coalesce(max_dload_cnt, 0)), 2)
            else 0 end as install_download_rate
        from (
            select imp_date,
              yyb_version,
              scene,
              adposid,
              coalesce(max(avg_exp_cnt), 0) as max_exp_cnt,
              coalesce(max(avg_click_cnt), 0) as max_click_cnt,
              coalesce(max(avg_dload_cnt), 0) as max_dload_cnt,
              coalesce(max(avg_install_cnt), 0) as max_install_cnt
            from (
                select imp_date,
                  yyb_version,
                  scene,
                  adposid,
                  sum(
                    case
                      when event_code = 'AppExposure' then num
                    end
                  ) avg_exp_cnt,
                  sum(
                    case
                      when event_code = 'AppClick' then num
                    end
                  ) avg_click_cnt,
                  sum(
                    case
                      when event_code = 'AppSuccDownload' then num
                    end
                  ) avg_dload_cnt,
                  sum(
                    case
                      when event_code = 'AppSuccInstall' then num
                    end
                  ) avg_install_cnt
                from (
                    select imp_date,
                      guid,
                      case
                        when {is_scene} = 1 then scene else 'all' end as scene,
                        adposid,
                        event_code,
                        case
                          when 'all' in ({version_conditions})
                            then 'all'
                            else yyb_version
                          end as yyb_version,
                          count(1) as num
                          from pcg_yyb_adbilling.dwd_yyb_business_event_log_di
                          where imp_date >= {start_date}
                            and imp_date <= {end_date}
                            and is_yyb = 1
                            and event_code in (
                              'AppExposure',
                              'AppClick',
                              'AppSuccDownload',
                              'AppSuccInstall'
                            )
                            and adposid in (
                              18,
                              34,
                              1,
                              1139,
                              1162,
                              13,
                              1021,
                              1160,
                              174,
                              1072,
                              1165,
                              799,
                              346
                            )
                          group by imp_date,
                            guid,
                            scene,
                            adposid,
                            event_code,
                            yyb_version
                        ) a
                        group by imp_date,
                          scene,
                          adposid,
                          yyb_version
                      ) t
                    where yyb_version in ({version_conditions})
                    group by imp_date,
                      scene,
                      adposid,
                      yyb_version
                  ) aggregated_by_adpos
                group by imp_date,
                  yyb_version,
                  scene
                order by imp_date desc
                LIMIT 5000
        """
