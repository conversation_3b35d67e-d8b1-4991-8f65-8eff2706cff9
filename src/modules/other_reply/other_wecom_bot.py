import json
from typing import Optional

from src.common.logs.logger import app_logger
from src.common.error.detailed_value_error import raise_value_error, ErrorCode
from src.client.hunyuan_client import model_client_manager
from src.modules.other_reply.prompt.other_prompt import OTHER_REPLY_PROMPT
from src.common.logs.loggers.chat_logger import chat_logger
from src.common.logs.loggers.state_logger import state_logger


class OtherWecomBot:
    def __init__(self):
        pass

    @staticmethod
    def generate_reply(query: str, chat_id: str = None) -> str:
        """
        根据用户输入生成回复

        :param query: 用户问题
        :param history: 历史对话，默认为空字符串
        :param current_state: 当前状态，默认为空字符串
        :param chat_id: 聊天会话ID，可选，用于获取历史记录和状态
        :return: 回复内容
        """

        history = chat_logger.get_formatted_chat_history(chat_id)
        current_state = state_logger.get_current_state_str(chat_id)
        
        # 拼接 prompt
        prompt = OTHER_REPLY_PROMPT.format(
            query=query,
            history=history if history else "无",
            current_state=current_state if current_state else "无",
        )
        model = "DeepSeek-V3-Online"

        try:
            # 使用最简单的调用接口，直接返回结果
            reply = model_client_manager.get_all_answer(prompt, model)
            
            if not reply or not reply.strip():
                raise_value_error(ErrorCode.USER_INTENT_SCENE_NOT_FOUND, message="模型返回的回复内容为空")
            
            return reply.strip()
            
        except Exception as e:
            app_logger.error(f"生成回复失败: {str(e)}")
            raise_value_error(ErrorCode.USER_INTENT_SCENE_NOT_FOUND, message=f"生成回复失败: {str(e)}")


if __name__ == "__main__":
    # 测试用例
    test_cases = [
        "你好",
        "今天天气怎么样？",
        "退出当前功能",
        "什么是灰度实验？",
        "谢谢你的帮助"
    ]

    bot = OtherWecomBot()
    
    print("=" * 60)
    print("测试 OtherWecomBot 回复生成")
    print("=" * 60)
    
    for i, query in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {query}")
        try:
            result = bot.generate_reply(query)
            print(f"回复结果: {result}")
        except Exception as e:
            print(f"生成失败: {str(e)}")
        print("-" * 40)
