#!/usr/bin/env python3
import re
import datetime
import json
from typing import Dict, Any, List, ClassVar, Set
from dataclasses import dataclass, field, fields


class GrayConfigParser:
    """
    灰度配置解析器，专门用于解析灰度测试配置的Markdown文档
    """

    @staticmethod
    def parse_gray_config_from_markdown(markdown_content: str) -> Dict[str, Any]:
        """
        从markdown文档解析灰度测试配置（紧凑表格格式）

        支持的markdown格式：
        # 灰度测试配置

        ## 新用户配置

        | 组别 | QUA版本 | RQD版本 |
        |------|---------|---------|
        | 实验组1 | TMAF_900_P_9135 | 9.0.0_9001130_9135 |
        | 实验组2 | TMAF_900_P_9136 | 9.0.0_9001130_9136 |
        | 实验组3 | TMAF_900_P_9137 | 9.0.0_9001130_9137 |
        | 对照组 | TMAF_900_P_9138 | 9.0.0_9001130_9138 |

        测试日期: 2025/06/13, 2025/06/14, 2025/06/15

        ## 老用户配置

        | 组别 | QUA版本 | RQD版本 |
        |------|---------|---------|
        | 实验组1 | TMAF_900_P_9139 | 9.0.0_9001130_9139 |
        | 实验组2 | TMAF_900_P_9140 | 9.0.0_9001130_9140 |
        | 实验组3 | TMAF_900_P_9141 | 9.0.0_9001130_9141 |
        | 对照组 | TMAF_900_P_9142 | 9.0.0_9001130_9142 |

        测试日期: 2025/06/13, 2025/06/14, 2025/06/15

        :param markdown_content: markdown格式的配置内容
        :return: 解析后的配置字典，格式为：
        {
            'new_user': {
                'qua_versions': ['实验组1_QUA', '实验组2_QUA', '实验组3_QUA', '对照组_QUA'],
                'rqd_versions': ['实验组1_RQD', '实验组2_RQD', '实验组3_RQD', '对照组_RQD'],
                'test_dates': ['2025/06/13', '2025/06/14', '2025/06/15']
            },
            'old_user': {
                'qua_versions': ['实验组1_QUA', '实验组2_QUA', '实验组3_QUA', '对照组_QUA'],
                'rqd_versions': ['实验组1_RQD', '实验组2_RQD', '实验组3_RQD', '对照组_RQD'],
                'test_dates': ['2025/06/13', '2025/06/14', '2025/06/15']
            }
        }
        """
        # 提取配置部分，忽略后续的报告内容
        config_content = GrayConfigParser._extract_config_section(markdown_content)

        config = {
            'new_user': {
                'qua_versions': [],
                'rqd_versions': [],
                'test_dates': []
            },
            'old_user': {
                'qua_versions': [],
                'rqd_versions': [],
                'test_dates': []
            }
        }

        # 分割成不同的用户类型配置段
        sections = re.split(r'##\s*(新用户配置|老用户配置)', config_content)

        for i, section in enumerate(sections):
            section = section.strip()
            if section in ['新用户配置', '老用户配置']:
                user_type = 'new_user' if section == '新用户配置' else 'old_user'
                # 解析下一个段落（配置内容）
                if i + 1 < len(sections):
                    GrayConfigParser._parse_user_config_section(sections[i + 1], config[user_type])

        return config, config_content

    @staticmethod
    def _extract_config_section(markdown_content: str) -> str:
        """
        从markdown文档中提取配置部分，忽略后续的报告内容

        配置部分通常在文档开头，以"# 灰度实验配置"或类似标题开始，
        在遇到"# 灰度数据报告"或其他报告标题时结束

        :param markdown_content: 完整的markdown文档内容
        :return: 仅包含配置的部分
        """
        # 查找配置结束的标志
        # 常见的报告开始标志
        report_markers = [
            r'#\s*异常数据分析',
            r'#\s*AI总结分析',
            r'#\s*异常数据',
            r'#\s*灰度新用户 数据',
            r'#\s*灰度老用户 数据'
        ]

        # 查找第一个报告标志的位置
        earliest_pos = len(markdown_content)
        for marker in report_markers:
            match = re.search(marker, markdown_content, re.IGNORECASE)
            if match:
                earliest_pos = min(earliest_pos, match.start())

        # 如果找到报告标志，只返回配置部分
        if earliest_pos < len(markdown_content):
            config_content = markdown_content[:earliest_pos].strip()
            print(f"检测到报告内容，仅解析配置部分（前{earliest_pos}字符）")
        else:
            config_content = markdown_content.strip()
            print(f"未检测到报告内容，解析完整文档（{len(markdown_content)}字符）")

        return config_content

    @staticmethod
    def _parse_user_config_section(section_content: str, user_config: Dict[str, List[str]]) -> None:
        """
        解析单个用户类型的配置段（紧凑表格格式）

        支持的格式：
        | 组别 | QUA版本 | RQD版本 |
        |------|---------|---------|
        | 实验组1 | TMAF_900_P_9135 | 9.0.0_9001130_9135 |
        | 实验组2 | TMAF_900_P_9136 | 9.0.0_9001130_9136 |
        | 实验组3 | TMAF_900_P_9137 | 9.0.0_9001130_9137 |
        | 对照组 | TMAF_900_P_9138 | 9.0.0_9001130_9138 |

        测试日期: 2025/06/13, 2025/06/14, 2025/06/15

        :param section_content: 配置段内容
        :param user_config: 要填充的用户配置字典
        """
        # 查找表格
        table_pattern = r'\|[^|]+\|[^|]+\|[^|]+\|'
        table_lines = re.findall(table_pattern, section_content)

        if len(table_lines) < 2:  # 至少需要表头和一行数据
            print(f"⚠️  未找到有效的表格格式，请检查配置")
            return

        # 解析表格数据
        qua_versions = []
        rqd_versions = []

        for line in table_lines[1:]:  # 跳过表头
            if '---' in line:  # 跳过分隔线
                continue

            parts = [part.strip() for part in line.split('|') if part.strip()]
            if len(parts) >= 3:
                # 格式: | 实验组1 | QUA版本 | RQD版本 |
                qua_versions.append(parts[1])
                rqd_versions.append(parts[2])

        if qua_versions and rqd_versions:
            user_config['qua_versions'] = qua_versions.copy()
            user_config['rqd_versions'] = rqd_versions.copy()

            # 查找测试日期
            date_pattern = r'测试日期[：:]\s*([^\n\r]+)'
            date_match = re.search(date_pattern, section_content)
            if date_match:
                dates_str = date_match.group(1).strip()
                # 移除可能的额外内容（如注释、说明等）
                dates_str = GrayConfigParser._clean_date_string(dates_str)
                # 支持逗号或空格分隔
                dates = [d.strip() for d in re.split(r'[,，\s]+', dates_str) if d.strip() and GrayConfigParser._is_valid_date(d.strip())]
                user_config['test_dates'] = dates.copy()
            else:
                print(f"⚠️  未找到测试日期，请检查配置")
        else:
            print(f"⚠️  表格解析失败，请检查表格格式")

    @staticmethod
    def _clean_date_string(dates_str: str) -> str:
        """
        清理日期字符串，移除可能的额外内容

        :param dates_str: 原始日期字符串
        :return: 清理后的日期字符串
        """
        # 移除常见的注释标记和说明文字
        # 匹配到第一个非日期内容就停止
        cleaned = dates_str

        # 移除括号及其内容
        cleaned = re.sub(r'\([^)]*\)', '', cleaned)
        cleaned = re.sub(r'（[^）]*）', '', cleaned)

        # 移除常见的注释标记
        for marker in ['#', '//', '<!--', '-->', '注：', '说明：', '备注：']:
            if marker in cleaned:
                cleaned = cleaned.split(marker)[0]

        # 移除多余的空白字符
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        return cleaned

    @staticmethod
    def _is_valid_date(date_str: str) -> bool:
        """
        验证是否为有效的日期格式和日期值

        :param date_str: 日期字符串
        :return: 是否为有效日期
        """
        # 支持的日期格式
        date_formats = [
            ('%Y/%m/%d', r'^\d{4}/\d{1,2}/\d{1,2}$'),      # YYYY/MM/DD
            ('%Y-%m-%d', r'^\d{4}-\d{1,2}-\d{1,2}$'),      # YYYY-MM-DD
            ('%Y.%m.%d', r'^\d{4}\.\d{1,2}\.\d{1,2}$'),    # YYYY.MM.DD
        ]

        # 首先检查格式
        for date_format, pattern in date_formats:
            if re.match(pattern, date_str):
                try:
                    # 尝试解析日期以验证有效性
                    datetime.datetime.strptime(date_str, date_format)
                    return True
                except ValueError:
                    # 日期格式匹配但值无效（如2025/13/45）
                    continue

        # 检查中文日期格式
        chinese_pattern = r'^(\d{4})年(\d{1,2})月(\d{1,2})日$'
        chinese_match = re.match(chinese_pattern, date_str)
        if chinese_match:
            try:
                year, month, day = chinese_match.groups()
                datetime.datetime(int(year), int(month), int(day))
                return True
            except ValueError:
                return False

        return False


@dataclass
class GrayTestConfig:
    """
    灰度测试配置
    """
    new_user_qua_versions: List[str] = field(default_factory=list)  # 新用户QUA版本列表
    new_user_rqd_versions: List[str] = field(default_factory=list)  # 新用户RQD版本列表
    new_user_test_dates: List[str] = field(default_factory=list)    # 新用户测试日期列表
    old_user_qua_versions: List[str] = field(default_factory=list)  # 老用户QUA版本列表
    old_user_rqd_versions: List[str] = field(default_factory=list)  # 老用户RQD版本列表
    old_user_test_dates: List[str] = field(default_factory=list)    # 老用户测试日期列表

    _required_fields: ClassVar[Set[str]] = frozenset()
    _title_map: ClassVar[Dict[str, str]] = {
        "new_user_qua_versions": "新用户QUA版本",
        "new_user_rqd_versions": "新用户RQD版本",
        "new_user_test_dates": "新用户测试日期",
        "old_user_qua_versions": "老用户QUA版本",
        "old_user_rqd_versions": "老用户RQD版本",
        "old_user_test_dates": "老用户测试日期"
    }

    def to_custom_config(self) -> Dict[str, Any]:
        """
        转换为 gray_data_collector.py 中使用的 custom_config 格式

        :return: custom_config 字典格式
        """
        return {
            'new_user': {
                'qua_versions': self.new_user_qua_versions.copy(),
                'rqd_versions': self.new_user_rqd_versions.copy(),
                'test_dates': self.new_user_test_dates.copy()
            },
            'old_user': {
                'qua_versions': self.old_user_qua_versions.copy(),
                'rqd_versions': self.old_user_rqd_versions.copy(),
                'test_dates': self.old_user_test_dates.copy()
            }
        }

    @classmethod
    def from_custom_config(cls, custom_config: Dict[str, Any]) -> 'GrayTestConfig':
        """
        从 custom_config 字典格式创建 GrayTestConfig 实例

        :param custom_config: custom_config 字典格式
        :return: GrayTestConfig 实例
        """
        return cls(
            new_user_qua_versions=custom_config.get('new_user', {}).get('qua_versions', []),
            new_user_rqd_versions=custom_config.get('new_user', {}).get('rqd_versions', []),
            new_user_test_dates=custom_config.get('new_user', {}).get('test_dates', []),
            old_user_qua_versions=custom_config.get('old_user', {}).get('qua_versions', []),
            old_user_rqd_versions=custom_config.get('old_user', {}).get('rqd_versions', []),
            old_user_test_dates=custom_config.get('old_user', {}).get('test_dates', [])
        )

    def to_dict(self) -> Dict[str, Any]:
        """
        将 dataclass 实例转换成字典，忽略以 _ 开头的字段。
        """
        return {
            f.name: getattr(self, f.name)
            for f in fields(self)
            if not f.name.startswith("_")
        }

    def to_json(self, **json_kwargs) -> str:
        """
        转成 JSON 字符串，默认 indent=2 方便阅读。
        """
        return json.dumps(self.to_dict(), ensure_ascii=False, indent=2, **json_kwargs)

    @classmethod
    def get_field_desc(cls, field_name: str) -> str:
        """
        获取字段描述，默认从子类的 _title_map 中查找。
        如果没有找到，返回字段名本身。
        """
        title_map = getattr(cls, "_title_map", {})
        return title_map.get(field_name, field_name)
