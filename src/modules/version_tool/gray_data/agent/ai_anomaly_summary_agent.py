#!/usr/bin/env python3
"""
AI异常总结Agent
负责生成异常数据的AI总结报告
"""

from typing import Generator, Dict, Any
from src.client.hunyuan_client import model_client_manager
from src.modules.version_tool.gray_data.prompt.ai_anomaly_summary_prompt import AI_ANOMALY_SUMMARY_PROMPT


class AIAnomalySummaryAgent:
    """AI异常总结Agent"""

    def __init__(self, model: str = "DeepSeek-R1-Online"):
        """
        初始化AI异常总结Agent

        Args:
            model: 使用的AI模型名称
        """
        self.model = model

    def generate_ai_summary(self, anomaly_summary: str) -> str:
        """
        生成AI异常总结报告

        Args:
            anomaly_summary: 原始异常数据汇总

        Returns:
            str: AI生成的异常总结报告
        """
        if not anomaly_summary or anomaly_summary.strip() == "未发现异常数据":
            return "未发现异常数据"
        
        print("-" * 80)
        print("原始异常数据汇总:")
        print("-" * 80)
        print(f'{anomaly_summary}')
        print("-" * 80)
        
        prompt = AI_ANOMALY_SUMMARY_PROMPT.format(anomaly_summary=anomaly_summary)
        print("AI异常总结prompt:")
        print("-" * 80)
        print(f'{prompt}')
        print("-" * 80)

        # 使用ModelClientManager的get_all_answer方法
        ai_summary = model_client_manager.get_all_answer(prompt, self.model)

        print("AI异常总结报告:")
        print("-" * 80)
        print(f'{ai_summary}')
        print("-" * 80)
        
        return ai_summary


def create_ai_anomaly_summary_agent(model: str = "DeepSeek-R1-Online") -> AIAnomalySummaryAgent:
    """
    创建AI异常总结Agent实例

    Args:
        model: 使用的AI模型名称

    Returns:
        AIAnomalySummaryAgent: AI异常总结Agent实例
    """
    return AIAnomalySummaryAgent(model=model)


if __name__ == "__main__":
    # 示例用法
    agent = create_ai_anomaly_summary_agent(model="DeepSeek-R1-Online")
    anomaly_summary1 = """
# [异常总结]
## Crash率异常
**老用户：**
- **实验组1** 第2天: `0.20%` (超过0.18%阈值(高出0.0186%)) `[QUA: 8.9.9_8994130_8549, 时间: 2025/06/07]`
- **实验组1** 第3天: `0.18%` (超过0.18%阈值(实际值:0.1817%)) `[QUA: 8.9.9_8994130_8549, 时间: 2025/06/08]`
- **实验组2** 第2天: `0.20%` (超过0.18%阈值(高出0.0152%)) `[QUA: 8.9.9_8994130_8551, 时间: 2025/06/07]`
- **实验组2** 第3天: `0.19%` (超过0.18%阈值(实际值:0.1875%)) `[QUA: 8.9.9_8994130_8551, 时间: 2025/06/08]`
"""

    anomaly_summary2 = """
# 异常数据

## Crash率异常
**老用户：**
- **实验组1** 第2天: `0.20%` (超过0.18%阈值(高出0.0186%)) `[QUA: 8.9.9_8994130_8549, 时间: 2025/06/07]`
- **实验组1** 第3天: `0.18%` (超过0.18%阈值(实际值:0.1817%)) `[QUA: 8.9.9_8994130_8549, 时间: 2025/06/08]`
- **实验组2** 第2天: `0.20%` (超过0.18%阈值(高出0.0152%)) `[QUA: 8.9.9_8994130_8551, 时间: 2025/06/07]`
- **实验组2** 第3天: `0.19%` (超过0.18%阈值(实际值:0.1875%)) `[QUA: 8.9.9_8994130_8551, 时间: 2025/06/08]`

## 启动速度异常
**新用户：**
- **实验组1** 常规外call冷启动: `3565.69 (176次)` (比对照组慢68.86ms) `[QUA: TMAF_899_P_8547, 时间: 2025/06/06~2025/06/08]`
- **实验组2** 常规外call冷启动: `3567.63 (163次)` (比对照组慢70.80ms) `[QUA: TMAF_899_P_8548, 时间: 2025/06/06~2025/06/08]`

**老用户：**
- **实验组1** 常规外call冷启动: `4825.99 (2566次)` (比对照组慢64.30ms) `[QUA: TMAF_899_P_8549, 时间: 2025/06/06~2025/06/08]`
"""

    anomaly_summary3 = """

# 异常数据

## 启动速度异常
**老用户：**
- **实验组1** 常规冷启动: `2576.77 (1930次)` (比对照组慢116.23ms) `[QUA: TMAF_899_P_9178, 时间: 2025/06/13~2025/06/16]`
- **实验组1** 常规外call冷启动: `5001.48 (514次)` (比对照组慢87.20ms) `[QUA: TMAF_899_P_9178, 时间: 2025/06/13~2025/06/16]`

## 下载安装CVR异常
**老用户：**
- **实验组1** 第1天: `58.06% (937数)` (比对照组低1.18%) `[QUA: TMAF_899_P_9178, 时间: 2025/06/13]`
- **实验组1** 第2天: `53.40% (2646数)` (比对照组低1.62%) `[QUA: TMAF_899_P_9178, 时间: 2025/06/14]`
- **实验组1** 第3天: `51.60% (2564数)` (比对照组低1.84%) `[QUA: TMAF_899_P_9178, 时间: 2025/06/15]`

## 广告转化率异常
**老用户：**
- **实验组1** 第1天: `145.74%` (比对照组低20.51%) `[QUA: TMAF_899_P_9178, 时间: 2025/06/13]`
"""
    anomaly_summary4 = """
# 异常数据

## ANR率异常
**新用户：**
- **实验组1** 第1天: `0.31%` (比对照组高0.08%(实验组:0.31% vs 对照组:0.23%)) `[QUA: 9.0.2_9021130_1448, 时间: 2025/07/10]`

**老用户：**
- **实验组3** 第1天: `0.26%` (比对照组高0.05%(实验组:0.26% vs 对照组:0.21%)) `[QUA: 9.0.2_9021130_1463, 时间: 2025/07/10]`
- **实验组3** 第2天: `0.32%` (比对照组高0.05%(实验组:0.32% vs 对照组:0.27%)) `[QUA: 9.0.2_9021130_1463, 时间: 2025/07/11]`
- **实验组3** 第3天: `0.32%` (比对照组高0.05%(实验组:0.32% vs 对照组:0.27%)) `[QUA: 9.0.2_9021130_1463, 时间: 2025/07/12]`

## 启动速度异常
**新用户：**
- **实验组1** 常规外call热启动: `2670.14 (215次)` (比对照组慢108.24ms) `[QUA: TMAF_902_P_1448, 时间: 2025/07/10~2025/07/12]`
- **实验组3** 常规外call热启动: `2651.93 (228次)` (比对照组慢90.03ms) `[QUA: TMAF_902_P_1450, 时间: 2025/07/10~2025/07/12]`

**老用户：**
- **实验组1** 常规冷启动: `3295.46 (39729次)` (比对照组慢72.85ms) `[QUA: TMAF_902_P_1461, 时间: 2025/07/10~2025/07/12]`
- **实验组2** 常规外call热启动: `3574.55 (7907次)` (比对照组慢52.30ms) `[QUA: TMAF_902_P_1462, 时间: 2025/07/10~2025/07/12]`
- **实验组3** 常规冷启动: `3313.16 (41631次)` (比对照组慢90.55ms) `[QUA: TMAF_902_P_1463, 时间: 2025/07/10~2025/07/12]`
- **实验组3** 常规外call冷启动: `4991.85 (3172次)` (比对照组慢134.45ms) `[QUA: TMAF_902_P_1463, 时间: 2025/07/10~2025/07/12]`
- **实验组3** 常规外call热启动: `3621.45 (8102次)` (比对照组慢99.20ms) `[QUA: TMAF_902_P_1463, 时间: 2025/07/10~2025/07/12]`
- **实验组3** 常规热启动: `2420.24 (59456次)` (比对照组慢77.92ms) `[QUA: TMAF_902_P_1463, 时间: 2025/07/10~2025/07/12]`

## 外call开始下载率异常
**老用户：**
- **实验组3** 第2天: `72.41% (6896户)` (比对照组低1.09%) `[QUA: TMAF_902_P_1463, 时间: 2025/07/11]`

## 外call成功下载率异常
**老用户：**
- **实验组2** 第1天: `83.09% (2413户)` (比对照组低1.23%) `[QUA: TMAF_902_P_1462, 时间: 2025/07/10]`
- **实验组2** 第3天: `83.13% (9081户)` (比对照组低1.03%) `[QUA: TMAF_902_P_1462, 时间: 2025/07/12]`

## 下载安装CVR异常
**老用户：**
- **实验组1** 第1天: `58.30% (18963数)` (比对照组低1.80%) `[QUA: TMAF_902_P_1461, 时间: 2025/07/10]`
- **实验组1** 第2天: `56.71% (39223数)` (比对照组低1.08%) `[QUA: TMAF_902_P_1461, 时间: 2025/07/11]`
- **实验组2** 第2天: `56.71% (39102数)` (比对照组低1.08%) `[QUA: TMAF_902_P_1462, 时间: 2025/07/11]`
"""

    ai_summary = agent.generate_ai_summary(anomaly_summary4)
    # print("=" * 80)
    # print("最终AI异常总结报告:")
    # print("=" * 80)
    # print(f'{ai_summary}')
    # print("=" * 80)
