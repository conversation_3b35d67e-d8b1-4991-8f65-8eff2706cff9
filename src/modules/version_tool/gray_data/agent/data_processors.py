#!/usr/bin/env python3
"""
数据处理
"""
import time
from typing import Dict, List, Any, Optional, Tuple
from abc import ABC, abstractmethod


class DataProcessor(ABC):
    """数据处理 抽象基类"""
    
    def __init__(self, config, anomaly_detector):
        self.config = config
        self.anomaly_detector = anomaly_detector
    
    @abstractmethod
    def process_data(self, raw_data: Any, versions: List[str], user_type: str, test_dates: List[str]) -> Any:
        """处理数据的抽象方法"""
        pass
    
    def _find_control_group_index(self, versions: List[str], raw_data: List = None) -> Optional[int]:
        """查找对照组索引"""
        for i in range(len(versions) - 1, -1, -1):
            if versions[i] != "/":
                if raw_data is None or i >= len(raw_data) or raw_data[i] is not None:
                    return i
        return None


class CrashAnrProcessor(DataProcessor):
    """Crash和ANR数据处理器"""

    def process_data(self, raw_data: Any, versions: List[str], user_type: str, test_dates: List[str]) -> Any:
        """实现抽象方法 - 处理crash和ANR数据"""
        # 这个方法作为接口保留，实际使用 process_crash_anr_data
        return self.process_crash_anr_data(versions, test_dates, user_type, raw_data)

    def process_crash_anr_data(self, rqd_versions: List[str], test_dates: List[str],
                              user_type: str, bugly_client) -> Tuple[List[List[str]], List[List[str]]]:
        """处理crash和ANR数据"""
        # 过滤掉无效的测试日期
        valid_test_dates = [date for date in test_dates if date and date != "/" and date != "none"]
        
        if not valid_test_dates:
            print(f"  收集crash和ANR数据: 没有有效的测试日期，使用默认数据")
            return [], []
        
        start_date = valid_test_dates[0].replace('/', '-')
        end_date = valid_test_dates[-1].replace('/', '-')
        
        print(f"  收集crash和ANR数据: {start_date} ~ {end_date}, RQD版本: {len(rqd_versions)}个")
        
        crash_data = []
        raw_anr_data = []
        
        # 第一轮：收集所有原始数据
        for i, rqd_version in enumerate(rqd_versions):
            if rqd_version == "/":
                crash_data.append(["/"] * 4)
                raw_anr_data.append([None] * 4)
                continue
            
            try:
                daily_data = bugly_client.get_crash_and_anr_rates_by_date(
                    start_date=start_date,
                    end_date=end_date,
                    product_versions=[rqd_version]
                )
                
                crash_rates = []
                anr_rates = []
                
                for day_idx, day in enumerate(daily_data):
                    crash_rate_value = day['crash_rate']
                    crash_rate = f"{crash_rate_value:.2%}"
                    
                    # 获取对应的测试日期
                    specific_date = valid_test_dates[day_idx] if day_idx < len(valid_test_dates) else "/"
                    
                    # 检查crash率异常 - 使用严格大于，避免浮点数精度问题
                    if crash_rate_value > self.config.crash_rate_threshold + self.config.float_comparison_epsilon:
                        # 创建用于表格显示的HTML格式
                        crash_rate = f'<span style="color: red;">{crash_rate}</span>'
                        group_name = f"实验组{i+1}" if i < 3 else "对照组"

                        # 创建用于异常记录的纯文本格式（不包含HTML标签）
                        anomaly_display_value = f"{crash_rate_value:.2%}"

                        # 计算超出阈值的精确数值，避免显示误解
                        threshold_diff = crash_rate_value - self.config.crash_rate_threshold
                        if threshold_diff < self.config.precision_tolerance:  # 如果差异很小，显示精确值
                            reason = f'超过{self.config.crash_rate_threshold:.2%}阈值(实际值:{crash_rate_value:.4%})'
                        else:
                            reason = f'超过{self.config.crash_rate_threshold:.2%}阈值(高出{threshold_diff:.4%})'

                        self.anomaly_detector.record_anomaly(
                            user_type, 'crash', group_name, f'第{day_idx+1}天', anomaly_display_value,
                            reason, qua_version=rqd_version, specific_date=specific_date
                        )
                    
                    anr_rate_value = day['anr_rate']
                    anr_rates.append(anr_rate_value)
                    crash_rates.append(crash_rate)
                
                # 补齐到4天
                crash_rates += ["/"] * (4 - len(crash_rates))
                anr_rates += [None] * (4 - len(anr_rates))
                
                crash_data.append(crash_rates)
                raw_anr_data.append(anr_rates)
                
            except Exception as e:
                print(f"       获取版本 {rqd_version} 的crash数据失败: {e}")
                crash_data.append(["/"] * 4)
                raw_anr_data.append([None] * 4)
        
        # 第二轮：处理ANR数据，进行实验组与对照组比较
        anr_data = self._process_anr_comparison(raw_anr_data, rqd_versions, user_type, valid_test_dates)
        
        print(f"     crash和ANR数据收集完成")
        return crash_data, anr_data
    
    def _process_anr_comparison(self, raw_anr_data: List[List], rqd_versions: List[str], 
                               user_type: str, test_dates: List[str]) -> List[List[str]]:
        """处理ANR率数据，进行实验组与对照组比较"""
        anr_data = []
        
        # 确定对照组索引
        control_group_index = self._find_control_group_index(rqd_versions, raw_anr_data)
        
        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for group_raw_anr in raw_anr_data:
                formatted_anr = []
                for anr_value in group_raw_anr:
                    if anr_value is None:
                        formatted_anr.append("/")
                    else:
                        formatted_anr.append(f"{anr_value:.2%}")
                anr_data.append(formatted_anr)
            return anr_data
        
        control_group_anr = raw_anr_data[control_group_index]
        
        # 处理每个组的ANR数据
        for i, group_raw_anr in enumerate(raw_anr_data):
            formatted_anr = []
            group_name = f"实验组{i+1}" if i < 3 else "对照组"
            
            for day_idx, anr_value in enumerate(group_raw_anr):
                if anr_value is None:
                    formatted_anr.append("/")
                    continue
                
                anr_formatted = f"{anr_value:.2%}"
                
                # 获取对应的测试日期
                specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                
                # 如果是实验组，与对照组比较
                if i != control_group_index and i < 3:
                    control_anr = control_group_anr[day_idx] if day_idx < len(control_group_anr) else None

                    if control_anr is not None:
                        # 基于显示格式的数值进行比较
                        anr_display_value = float(f"{anr_value:.4f}")
                        control_anr_display_value = float(f"{control_anr:.4f}")
                        anr_diff = anr_display_value - control_anr_display_value

                        if anr_diff >= self.config.anr_rate_threshold:
                            # 创建用于表格显示的HTML格式
                            anr_formatted = f'<span style="color: red;">{anr_formatted}</span>'

                            # 创建用于异常记录的纯文本格式（不包含HTML标签）
                            anomaly_display_value = f"{anr_value:.2%}"

                            # 提供更清晰的ANR率差异说明，包含对照组数值
                            if anr_diff < self.config.precision_tolerance:  # 差异很小时显示精确值
                                reason = f'比对照组高{anr_diff:.4%}(实验组:{anr_value:.4%} vs 对照组:{control_anr:.4%})'
                            else:
                                reason = f'比对照组高{anr_diff:.2%}(实验组:{anr_value:.2%} vs 对照组:{control_anr:.2%})'

                            self.anomaly_detector.record_anomaly(
                                user_type, 'anr', group_name, f'第{day_idx+1}天', anomaly_display_value,
                                reason, qua_version=rqd_versions[i], specific_date=specific_date
                            )
                
                formatted_anr.append(anr_formatted)
            
            anr_data.append(formatted_anr)
        
        return anr_data


class LaunchSpeedProcessor(DataProcessor):
    """启动速度数据处理器"""

    def process_data(self, raw_data: Any, versions: List[str], user_type: str, test_dates: List[str]) -> Any:
        """实现抽象方法 - 处理启动速度数据"""
        return self.process_launch_speed_data(versions, test_dates, user_type, raw_data)

    def process_launch_speed_data(self, qua_versions: List[str], test_dates: List[str],
                                 user_type: str, beacon_client) -> Dict[str, List[str]]:
        """处理启动速度数据"""
        # 过滤掉占位符版本
        valid_qua_versions = [qua for qua in qua_versions if qua != "/"]
        valid_test_dates = [date for date in test_dates if date and date != "/" and date != "none"]
        
        print(f"  收集{user_type}用户启动速度数据: {len(qua_versions)}个QUA版本")
        
        if not valid_qua_versions or not valid_test_dates:
            print(f"     没有有效的QUA版本或测试日期，使用默认数据")
            return {
                'regular_hot': ["/"] * 4,
                'regular_cold': ["/"] * 4,
                'outcall_hot': ["/"] * 4,
                'outcall_cold': ["/"] * 4
            }
        
        # 添加重试机制
        max_retries = 2
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                start_date = valid_test_dates[0].replace('/', '-')
                end_date = valid_test_dates[-1].replace('/', '-')
                
                launch_speed_data = beacon_client.query_launch_speed_analytics(
                    qua_list=valid_qua_versions,
                    start_date=start_date,
                    end_date=end_date
                )
                
                # 收集原始数据
                raw_speed_data = []
                for qua_version in qua_versions:
                    if qua_version == "/":
                        raw_speed_data.append(None)
                    else:
                        if user_type == 'new':
                            speed_data = beacon_client.get_new_user_launch_speed(launch_speed_data, qua_version)
                        else:
                            speed_data = beacon_client.get_old_user_launch_speed(launch_speed_data, qua_version)
                        raw_speed_data.append(speed_data)
                
                # 进行实验组与对照组比较
                result = self._process_launch_speed_comparison(raw_speed_data, qua_versions, user_type, valid_test_dates)
                
                print(f"     {user_type}用户启动速度数据收集完成")
                return result
                
            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"     获取启动速度数据失败: {e}，正在重试 ({retry_count}/{max_retries})...")
                    time.sleep(5)  # 等待5秒后重试
                else:
                    print(f"     获取启动速度数据失败: {e}")
                    return {
                        'regular_hot': ["/"] * 4,
                        'regular_cold': ["/"] * 4,
                        'outcall_hot': ["/"] * 4,
                        'outcall_cold': ["/"] * 4
                    }
    
    def _process_launch_speed_comparison(self, raw_speed_data: List, qua_versions: List[str], 
                                       user_type: str, test_dates: List[str]) -> Dict[str, List[str]]:
        """处理启动速度数据，进行实验组与对照组比较"""
        result = {
            'regular_hot': [],
            'regular_cold': [],
            'outcall_hot': [],
            'outcall_cold': []
        }
        
        # 确定对照组索引
        control_group_index = self._find_control_group_index(qua_versions, raw_speed_data)
        
        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for speed_data in raw_speed_data:
                if speed_data is None:
                    for key in result:
                        result[key].append("/")
                else:
                    result['regular_hot'].append(f"{speed_data.get('常规热启动', {}).get('平均值', 0):.2f}")
                    result['regular_cold'].append(f"{speed_data.get('常规冷启动', {}).get('平均值', 0):.2f}")
                    result['outcall_hot'].append(f"{speed_data.get('常规外call热启动', {}).get('平均值', 0):.2f}")
                    result['outcall_cold'].append(f"{speed_data.get('常规外call冷启动', {}).get('平均值', 0):.2f}")
            return result
        
        control_group_speed = raw_speed_data[control_group_index]
        
        # 处理每个组的启动速度数据
        for i, speed_data in enumerate(raw_speed_data):
            group_name = f"实验组{i+1}" if i < 3 else "对照组"
            
            if speed_data is None:
                for key in result:
                    result[key].append("/")
                continue
            
            # 处理各种启动类型
            launch_types = [
                ('常规热启动', 'regular_hot'),
                ('常规冷启动', 'regular_cold'),
                ('常规外call热启动', 'outcall_hot'),
                ('常规外call冷启动', 'outcall_cold')
            ]
            
            for launch_type_name, result_key in launch_types:
                metrics = speed_data.get(launch_type_name, {})
                avg_time = metrics.get('平均值', 0)
                count = metrics.get('次数', 0)

                # 基础格式化（不包含HTML）
                base_formatted_speed = f"{avg_time:.2f}"
                display_formatted_speed = base_formatted_speed

                # 如果是实验组，与对照组比较
                if i != control_group_index and i < 3:
                    control_metrics = control_group_speed.get(launch_type_name, {})
                    control_avg_time = control_metrics.get('平均值', 0)

                    if control_avg_time > 0:
                        speed_diff = avg_time - control_avg_time
                        if speed_diff >= self.config.launch_speed_threshold:
                            # 创建用于显示的HTML格式（仅用于表格显示）
                            display_formatted_speed = f'<span style="color: red;">{base_formatted_speed}<br>({count}次)</span>'

                            # 创建用于异常记录的纯文本格式（不包含HTML标签）
                            anomaly_display_value = f'{base_formatted_speed} ({count}次)'

                            # 记录异常数据
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            date_range = f"{test_dates[0]}~{test_dates[-1]}" if len(test_dates) > 1 else test_dates[0] if test_dates else "/"
                            self.anomaly_detector.record_anomaly(
                                user_type, 'launch_speed', group_name, launch_type_name,
                                anomaly_display_value, f'比对照组慢{speed_diff:.2f}ms',
                                qua_version=qua_version, specific_date=date_range
                            )

                # 使用显示格式添加到结果中
                result[result_key].append(display_formatted_speed)
        
        return result


class DownloadProcessor(DataProcessor):
    """下载数据处理器"""

    def process_data(self, raw_data: Any, versions: List[str], user_type: str, test_dates: List[str]) -> Any:
        """实现抽象方法 - 处理下载数据"""
        return self.process_download_data(versions, test_dates, user_type, raw_data)

    def process_download_data(self, qua_versions: List[str], test_dates: List[str],
                             user_type: str, beacon_client) -> Dict[str, List[List[str]]]:
        """处理下载数据"""
        # 过滤掉占位符版本
        valid_qua_versions = [qua for qua in qua_versions if qua != "/"]
        valid_test_dates = [date for date in test_dates if date and date != "/" and date != "none"]

        print(f"  收集{user_type}用户下载数据: {len(qua_versions)}个QUA版本")

        if not valid_qua_versions or not valid_test_dates:
            print(f"     没有有效的QUA版本或测试日期，使用默认数据")
            return {
                'start_rates': [["/"] * 4 for _ in range(4)],
                'success_rates': [["/"] * 4 for _ in range(4)],
                'cvr_rates': [["/"] * 4 for _ in range(4)]
            }

        # 添加重试机制
        max_retries = 2
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 格式化日期
                start_date = valid_test_dates[0].replace('/', '-')
                end_date = valid_test_dates[-1].replace('/', '-')

                # 收集外call下载数据
                print(f"     收集外call下载数据...")
                external_download_data = beacon_client.query_external_call_download_data(
                    start_date=start_date,
                    end_date=end_date,
                    qua_list=valid_qua_versions
                )

                # 收集下载安装CVR数据
                print(f"     收集下载安装CVR数据...")
                cvr_data = beacon_client.query_download_install_cvr(
                    start_date=start_date,
                    end_date=end_date,
                    qua_list=valid_qua_versions
                )

                # 进行实验组与对照组比较
                result = self._process_real_download_data(external_download_data, cvr_data, qua_versions, user_type, test_dates)

                print(f"     {user_type}用户下载数据收集完成")
                return result

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"     获取下载数据失败: {e}，正在重试 ({retry_count}/{max_retries})...")
                    time.sleep(5)  # 等待5秒后重试
                else:
                    print(f"     获取下载数据失败: {e}")
                    return {
                        'start_rates': [["/"] * 4 for _ in range(4)],
                        'success_rates': [["/"] * 4 for _ in range(4)],
                        'cvr_rates': [["/"] * 4 for _ in range(4)]
                    }

    def _process_real_download_data(self, external_download_data: Dict, cvr_data: Dict,
                                  qua_versions: List[str], user_type: str, test_dates: List[str]) -> Dict[str, List[List[str]]]:
        """处理真实的下载数据"""
        # 初始化结果结构
        result = {
            'start_rates': [[] for _ in range(4)],
            'success_rates': [[] for _ in range(4)],
            'cvr_rates': [[] for _ in range(4)]
        }

        # 处理外call下载数据（开始下载率、成功下载率）
        if external_download_data:
            self._process_external_download_data(external_download_data, qua_versions, result, user_type, test_dates)
        else:
            # 如果没有外call下载数据，填充默认值
            for i in range(4):
                result['start_rates'][i] = ["/"] * 4
                result['success_rates'][i] = ["/"] * 4

        # 处理CVR数据
        if cvr_data:
            self._process_cvr_data(cvr_data, qua_versions, result, user_type, test_dates)
        else:
            # 如果没有CVR数据，填充默认值
            for i in range(4):
                result['cvr_rates'][i] = ["/"] * 4

        return result

    def _process_external_download_data(self, external_download_data: Dict[str, Any], qua_versions: List[str],
                                      result: Dict[str, List[List[str]]], user_type: str, test_dates: List[str]) -> None:
        """处理外call下载数据 - 支持实验组与对照组比较"""
        try:
            # 第一轮：收集所有原始数据
            raw_download_data = []
            for qua_version in qua_versions:
                qua_raw_data = []
                for date in sorted(external_download_data.keys()):
                    if qua_version == "/" or qua_version not in external_download_data[date]:
                        qua_raw_data.append(None)
                    else:
                        version_data = external_download_data[date][qua_version]
                        start_rate_str = version_data.get('外call开始下载率', '0%')
                        success_rate_str = version_data.get('外call成功下载率', '0%')

                        # 处理None值和百分号格式
                        if start_rate_str is None or start_rate_str == 'none':
                            start_rate_str = "0.00%"
                        if success_rate_str is None or success_rate_str == 'none':
                            success_rate_str = "0.00%"

                        # 提取数值用于比较
                        start_rate_value = float(start_rate_str.replace('%', '')) / 100 if start_rate_str != "0.00%" else 0.0
                        success_rate_value = float(success_rate_str.replace('%', '')) / 100 if success_rate_str != "0.00%" else 0.0

                        qua_raw_data.append({
                            'start_rate_str': start_rate_str,
                            'success_rate_str': success_rate_str,
                            'start_rate_value': start_rate_value,
                            'success_rate_value': success_rate_value,
                            'start_count': version_data.get('开始下载户数', 0),
                            'success_count': version_data.get('成功下载户数', 0)
                        })
                raw_download_data.append(qua_raw_data)

            # 第二轮：进行实验组与对照组比较，生成最终格式化数据
            self._process_download_comparison(raw_download_data, qua_versions, result, sorted(external_download_data.keys()), user_type, test_dates)
        except (IndexError, KeyError) as e:
            print(f"     处理外call下载数据时发生错误: {e}")
            # 填充默认值
            for i in range(4):
                result['start_rates'][i] = ["/"] * 4
                result['success_rates'][i] = ["/"] * 4

    def _process_download_comparison(self, raw_download_data: List[List], qua_versions: List[str],
                                   result: Dict[str, List[List[str]]], sorted_dates: List[str], user_type: str, test_dates: List[str]) -> None:
        """处理下载数据，进行实验组与对照组比较"""
        # 确定对照组索引（通常是最后一个非占位符版本）
        control_group_index = self._find_control_group_index(qua_versions, raw_download_data)

        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for i, qua_data in enumerate(raw_download_data):
                for day_idx, day_data in enumerate(qua_data):
                    if day_data is None:
                        result['start_rates'][i].append("/")
                        result['success_rates'][i].append("/")
                    else:
                        result['start_rates'][i].append(day_data['start_rate_str'])
                        result['success_rates'][i].append(day_data['success_rate_str'])
            return

        # 获取对照组数据
        control_group_download = raw_download_data[control_group_index]

        # 有对照组的情况下进行完整比较
        for i, qua_data in enumerate(raw_download_data):
            group_name = f"实验组{i+1}" if i < 3 else "对照组"

            if qua_data is None or all(day is None for day in qua_data):
                # 占位符组
                for _ in range(len(sorted_dates)):
                    result['start_rates'][i].append("/")
                    result['success_rates'][i].append("/")
                continue

            # 处理每天的数据
            for day_idx, (date, day_data) in enumerate(zip(sorted_dates, qua_data)):
                if day_data is None:
                    result['start_rates'][i].append("/")
                    result['success_rates'][i].append("/")
                    continue

                # 开始下载率数据（进行实验组与对照组比较）
                start_rate_value = day_data['start_rate_value']
                start_count = day_data['start_count']
                start_rate_formatted = day_data['start_rate_str']

                # 如果是实验组，与对照组比较
                if i != control_group_index and i < 3:  # 实验组（前3组）
                    if (day_idx < len(control_group_download) and
                        control_group_download[day_idx] is not None):
                        control_day_data = control_group_download[day_idx]
                        control_start_rate = control_day_data['start_rate_value']

                        if control_start_rate > 0:
                            # 基于显示格式的数值进行比较（百分比）
                            start_rate_display_value = float(f"{start_rate_value * 100:.2f}")
                            control_start_rate_display_value = float(f"{control_start_rate * 100:.2f}")
                            rate_diff_display = control_start_rate_display_value - start_rate_display_value

                            threshold = self.config.download_rate_threshold_new if user_type == 'new' else self.config.download_rate_threshold_old

                            if rate_diff_display >= threshold:
                                # 创建用于表格显示的HTML格式
                                start_rate_formatted = f'<span style="color: red;">{start_rate_formatted}<br>({start_count}户)</span>'

                                # 记录异常数据
                                qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                                # 获取对应的测试日期
                                specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                                self.anomaly_detector.record_anomaly(
                                    user_type, 'download_start', group_name, f'第{day_idx+1}天',
                                    f'{start_rate_value * 100:.2f}% ({start_count}户)',
                                    f'比对照组低{rate_diff_display:.2f}%',
                                    qua_version=qua_version, specific_date=specific_date
                                )

                result['start_rates'][i].append(start_rate_formatted)

                # 成功下载率数据处理
                success_rate_value = day_data['success_rate_value']
                success_count = day_data['success_count']
                success_rate_formatted = day_data['success_rate_str']

                # 如果是实验组，与对照组比较成功下载率
                if i != control_group_index and i < 3:  # 实验组（前3组）
                    if (day_idx < len(control_group_download) and
                        control_group_download[day_idx] is not None):
                        control_day_data = control_group_download[day_idx]
                        control_success_rate = control_day_data['success_rate_value']

                        if control_success_rate > 0:
                            # 基于显示格式的数值进行比较（百分比）
                            success_rate_display_value = float(f"{success_rate_value * 100:.2f}")
                            control_success_rate_display_value = float(f"{control_success_rate * 100:.2f}")
                            success_rate_diff_display = control_success_rate_display_value - success_rate_display_value

                            # 根据用户类型选择不同的阈值
                            threshold = self.config.download_rate_threshold_new if user_type == 'new' else self.config.download_rate_threshold_old

                            if success_rate_diff_display >= threshold:
                                # 创建用于表格显示的HTML格式
                                success_rate_formatted = f'<span style="color: red;">{success_rate_formatted}<br>({success_count}户)</span>'

                                # 记录异常数据
                                qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                                # 获取对应的测试日期
                                specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                                self.anomaly_detector.record_anomaly(
                                    user_type, 'download_success', group_name, f'第{day_idx+1}天',
                                    f'{success_rate_value * 100:.2f}% ({success_count}户)',
                                    f'比对照组低{success_rate_diff_display:.2f}%',
                                    qua_version=qua_version, specific_date=specific_date
                                )

                result['success_rates'][i].append(success_rate_formatted)

    def _process_cvr_data(self, cvr_data: Dict[str, Any], qua_versions: List[str],
                         result: Dict[str, List[List[str]]], user_type: str, test_dates: List[str]) -> None:
        """处理CVR数据 - 支持实验组与对照组比较"""
        try:
            # 第一轮：收集所有原始数据
            raw_cvr_data = []
            for qua_version in qua_versions:
                qua_raw_data = []
                for date in sorted(cvr_data.keys()):
                    if qua_version == "/" or qua_version not in cvr_data[date]:
                        qua_raw_data.append(None)
                    else:
                        version_data = cvr_data[date][qua_version]
                        download_install_cvr = version_data.get('下载安装CVR', 0)
                        start_download_count = version_data.get('开始下载数', 0)
                        qua_raw_data.append({
                            'cvr_value': download_install_cvr,
                            'start_download_count': start_download_count
                        })
                raw_cvr_data.append(qua_raw_data)

            # 第二轮：进行实验组与对照组比较，生成最终格式化数据
            self._process_cvr_comparison(raw_cvr_data, qua_versions, result, sorted(cvr_data.keys()), user_type, test_dates)
        except (IndexError, KeyError) as e:
            print(f"     处理CVR数据时发生错误: {e}")
            # 填充默认值
            for i in range(4):
                result['cvr_rates'][i] = ["/"] * 4

    def _process_cvr_comparison(self, raw_cvr_data: List[List], qua_versions: List[str],
                               result: Dict[str, List[List[str]]], sorted_dates: List[str], user_type: str, test_dates: List[str]) -> None:
        """处理CVR数据，进行实验组与对照组比较"""
        # 确定对照组索引（通常是最后一个非占位符版本）
        control_group_index = self._find_control_group_index(qua_versions, raw_cvr_data)

        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for i, qua_data in enumerate(raw_cvr_data):
                for day_idx, day_data in enumerate(qua_data):
                    if day_data is None:
                        result['cvr_rates'][i].append("/")
                    else:
                        result['cvr_rates'][i].append(f"{day_data['cvr_value']:.2f}%")
            return

        # 获取对照组数据
        control_group_cvr = raw_cvr_data[control_group_index]

        # 有对照组的情况下进行完整比较
        for i, qua_data in enumerate(raw_cvr_data):
            group_name = f"实验组{i+1}" if i < 3 else "对照组"

            if qua_data is None or all(day is None for day in qua_data):
                # 占位符组
                for _ in range(len(sorted_dates)):
                    result['cvr_rates'][i].append("/")
                continue

            # 处理每天的数据
            for day_idx, (date, day_data) in enumerate(zip(sorted_dates, qua_data)):
                if day_data is None:
                    result['cvr_rates'][i].append("/")
                    continue

                # CVR数据（进行实验组与对照组比较）
                cvr_value = day_data['cvr_value']
                start_download_count = day_data['start_download_count']
                cvr_formatted = f"{cvr_value:.2f}%"

                # 如果是实验组，与对照组比较
                if i != control_group_index and i < 3:  # 实验组（前3组）
                    if (day_idx < len(control_group_cvr) and
                        control_group_cvr[day_idx] is not None):
                        control_day_data = control_group_cvr[day_idx]
                        control_cvr_value = control_day_data['cvr_value']

                        # 基于显示格式的数值进行比较
                        cvr_display_value = float(f"{cvr_value:.2f}")
                        control_cvr_display_value = float(f"{control_cvr_value:.2f}")
                        cvr_diff = control_cvr_display_value - cvr_display_value

                        if cvr_diff >= self.config.cvr_threshold:
                            # 创建用于表格显示的HTML格式
                            cvr_formatted = f'<span style="color: red;">{cvr_formatted}<br>({start_download_count}数)</span>'

                            # 记录异常数据
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            # 获取对应的测试日期
                            specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                            self.anomaly_detector.record_anomaly(
                                user_type, 'cvr', group_name, f'第{day_idx+1}天',
                                f'{cvr_value:.2f}% ({start_download_count}数)',
                                f'比对照组低{cvr_diff:.2f}%',
                                qua_version=qua_version, specific_date=specific_date
                            )

                result['cvr_rates'][i].append(cvr_formatted)


class OtherDataProcessor(DataProcessor):
    """其他数据处理器（联网、弹窗、云游戏）"""

    def process_data(self, raw_data: Any, versions: List[str], user_type: str, test_dates: List[str]) -> Any:
        """实现抽象方法 - 处理其他数据"""
        return self.process_other_data(versions, test_dates, [], raw_data)

    def process_other_data(self, qua_versions: List[str], test_dates: List[str],
                          rqd_versions: List[str], beacon_client, user_type: str = 'unknown') -> Dict[str, List[List[str]]]:
        """处理其他数据（联网、弹窗、云游戏）"""
        # 过滤掉占位符版本
        valid_qua_versions = [qua for qua in qua_versions if qua != "/"]
        valid_test_dates = [date for date in test_dates if date and date != "/" and date != "none"]

        print(f"  收集其他数据: {len(qua_versions)}个QUA版本")

        if not valid_qua_versions or not valid_test_dates:
            print(f"     没有有效的QUA版本或测试日期，使用默认数据")
            default_matrix = [["/"] * 4 for _ in range(4)]
            return {
                'net_users': [row[:] for row in default_matrix],
                'popup_rate': [row[:] for row in default_matrix],
                'cloud_game_rate': [row[:] for row in default_matrix]
            }

        # 添加重试机制
        max_retries = 2
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 格式化日期
                start_date = valid_test_dates[0].replace('/', '-')
                end_date = valid_test_dates[-1].replace('/', '-')

                # 收集联网数据
                print(f"     收集联网数据...")
                net_data = beacon_client.query_online_user_count(
                    start_date=start_date,
                    end_date=end_date,
                    qua_list=valid_qua_versions
                )

                # 收集弹窗数据
                print(f"     收集弹窗数据...")
                popup_data = beacon_client.query_popup_success_rate(
                    start_date=start_date,
                    end_date=end_date,
                    qua_list=valid_qua_versions,
                    user_type_list=['remain', 'silent_back']
                )

                # 收集云游戏数据 - 使用RQD版本而不是QUA版本
                print(f"     收集云游戏数据...")
                # 获取有效的RQD版本
                valid_rqd_versions = [rqd for rqd in rqd_versions if rqd != "/"]
                cloud_game_data = beacon_client.query_cloud_gaming_plugin_launch_success_rate(
                    start_date=start_date,
                    end_date=end_date,
                    app_version_list=valid_rqd_versions
                )

                # 处理数据 - 传递RQD版本给云游戏数据处理
                result = self._process_real_other_data(net_data, popup_data, cloud_game_data, qua_versions, rqd_versions, user_type, test_dates)

                print(f"     其他数据收集完成")
                return result

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"     获取其他数据失败: {e}，正在重试 ({retry_count}/{max_retries})...")
                    time.sleep(5)  # 等待5秒后重试
                else:
                    print(f"     获取其他数据失败: {e}")
                    default_matrix = [["/"] * 4 for _ in range(4)]
                    return {
                        'net_users': [row[:] for row in default_matrix],
                        'popup_rate': [row[:] for row in default_matrix],
                        'cloud_game_rate': [row[:] for row in default_matrix]
                    }

    def _process_real_other_data(self, net_data: Dict, popup_data: Dict,
                               cloud_game_data: Dict, qua_versions: List[str], rqd_versions: List[str], user_type: str, test_dates: List[str]) -> Dict[str, List[List[str]]]:
        """处理真实的其他数据"""
        # 初始化结果结构
        result = {
            'net_users': [[] for _ in range(4)],
            'popup_rate': [[] for _ in range(4)],
            'cloud_game_rate': [[] for _ in range(4)]
        }

        # 处理联网数据
        if net_data:
            self._process_net_data(net_data, qua_versions, result)
        else:
            # 如果没有联网数据，填充默认值
            for i in range(4):
                result['net_users'][i] = ["/"] * 4

        # 处理弹窗数据
        if popup_data:
            self._process_popup_data(popup_data, qua_versions, result, user_type, test_dates)
        else:
            # 如果没有弹窗数据，填充默认值
            for i in range(4):
                result['popup_rate'][i] = ["/"] * 4

        # 处理云游戏数据 - 使用RQD版本
        if cloud_game_data:
            self._process_cloud_game_data(cloud_game_data, rqd_versions, result, user_type, test_dates)
        else:
            # 如果没有云游戏数据，填充默认值
            for i in range(4):
                result['cloud_game_rate'][i] = ["/"] * 4

        return result

    def _process_net_data(self, net_data: Dict[str, Any], qua_versions: List[str], result: Dict[str, List[List[str]]]) -> None:
        """处理联网数据 - 使用统一排序策略"""
        # 统一使用升序排序
        for date in sorted(net_data.keys()):
            for i, qua_version in enumerate(qua_versions):
                if qua_version == "/" or qua_version not in net_data[date]:
                    result['net_users'][i].append("/")
                else:
                    user_count = net_data[date][qua_version]
                    result['net_users'][i].append(str(user_count))

    def _process_popup_data(self, popup_data: Dict[str, Any], qua_versions: List[str], result: Dict[str, List[List[str]]], user_type: str, test_dates: List[str]) -> None:
        """处理弹窗数据 - 支持实验组与对照组比较"""
        # 第一轮：收集所有原始数据
        raw_popup_data = []
        for qua_version in qua_versions:
            qua_raw_data = []
            for date in sorted(popup_data.keys()):
                if qua_version == "/" or qua_version not in popup_data[date]:
                    qua_raw_data.append(None)
                else:
                    qua_data = popup_data[date][qua_version]
                    success_rate = qua_data.get('弹窗成功率(%)', 0)
                    pop_times = qua_data.get('弹窗人数', 0)
                    expose_times = qua_data.get('曝光人数', 0)
                    qua_raw_data.append({
                        'success_rate': success_rate,
                        'pop_times': pop_times,
                        'expose_times': expose_times
                    })
            raw_popup_data.append(qua_raw_data)

        # 第二轮：进行实验组与对照组比较，生成最终格式化数据
        self._process_popup_comparison(raw_popup_data, qua_versions, result, sorted(popup_data.keys()), user_type, test_dates)

    def _process_popup_comparison(self, raw_popup_data: List[List], qua_versions: List[str], result: Dict[str, List[List[str]]], sorted_dates: List[str], user_type: str, test_dates: List[str]) -> None:
        """处理弹窗数据，进行实验组与对照组比较"""
        # 确定对照组索引（通常是最后一个非占位符版本）
        control_group_index = None
        for i in range(len(qua_versions) - 1, -1, -1):
            if qua_versions[i] != "/" and raw_popup_data[i] is not None:
                control_group_index = i
                break

        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for i, qua_data in enumerate(raw_popup_data):
                for day_idx, day_data in enumerate(qua_data):
                    if day_data is None:
                        result['popup_rate'][i].append("/")
                    else:
                        success_rate = day_data['success_rate']
                        result['popup_rate'][i].append(f"{success_rate:.2f}%")
            return

        control_group_popup = raw_popup_data[control_group_index]

        # 处理每个组的弹窗数据
        for i, qua_data in enumerate(raw_popup_data):
            group_name = f"实验组{i+1}" if i < 3 else "对照组"

            if qua_data is None or all(day is None for day in qua_data):
                # 占位符组
                for _ in range(len(sorted_dates)):
                    result['popup_rate'][i].append("/")
                continue

            # 处理每天的数据
            for day_idx, day_data in enumerate(qua_data):
                if day_data is None:
                    result['popup_rate'][i].append("/")
                    continue

                success_rate = day_data['success_rate']
                pop_times = day_data['pop_times']
                expose_times = day_data['expose_times']
                formatted_rate = f"{success_rate:.2f}%"

                # 如果是实验组，与对照组比较
                if i != control_group_index and i < 3:  # 实验组（前3组）
                    if (day_idx < len(control_group_popup) and
                        control_group_popup[day_idx] is not None):
                        control_day_data = control_group_popup[day_idx]
                        control_success_rate = control_day_data['success_rate']

                        # 弹窗成功率阈值检测（如果实验组比对照组低超过阈值）
                        if control_success_rate - success_rate >= self.config.popup_rate_threshold:
                            # 创建用于表格显示的HTML格式，标注弹窗人数和曝光次数
                            formatted_rate = f'<span style="color: red;">{formatted_rate}<br>({pop_times}弹窗/{expose_times}曝光)</span>'

                            # 创建用于异常记录的纯文本格式（不包含HTML标签）
                            anomaly_display_value = f"{success_rate:.2f}% ({pop_times}弹窗/{expose_times}曝光)"

                            # 记录异常数据
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            # 获取对应的测试日期
                            specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                            self.anomaly_detector.record_anomaly(
                                user_type, 'popup', group_name, f'第{day_idx+1}天',
                                anomaly_display_value, f'比对照组低{control_success_rate - success_rate:.2f}%',
                                qua_version=qua_version, specific_date=specific_date
                            )

                result['popup_rate'][i].append(formatted_rate)

    def _process_cloud_game_data(self, cloud_game_data: Dict[str, Any], rqd_versions: List[str], result: Dict[str, List[List[str]]], user_type: str, test_dates: List[str]) -> None:
        """处理云游戏数据 - 支持阈值检测"""
        # 统一使用升序排序
        sorted_dates = sorted(cloud_game_data.keys())
        for day_idx, date in enumerate(sorted_dates):
            for i, rqd_version in enumerate(rqd_versions):
                if rqd_version == "/" or rqd_version not in cloud_game_data[date]:
                    result['cloud_game_rate'][i].append("/")
                else:
                    app_data = cloud_game_data[date][rqd_version]
                    success_rate = app_data.get('云游插件拉起成功率', 0)

                    # 转换为百分比（API返回的是小数，如0.98表示98%）
                    success_rate_percent = success_rate * 100
                    formatted_rate = f"{success_rate_percent:.2f}%"

                    # 云游戏成功率阈值检测（如果低于阈值）
                    if success_rate_percent < self.config.cloud_game_threshold:
                        # 创建用于表格显示的HTML格式
                        formatted_rate = f'<span style="color: red;">{formatted_rate}</span>'

                        # 创建用于异常记录的纯文本格式（不包含HTML标签）
                        anomaly_display_value = f"{success_rate_percent:.2f}%"

                        # 记录异常数据
                        group_name = f"实验组{i+1}" if i < 3 else "对照组"
                        # 获取对应的测试日期
                        specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                        self.anomaly_detector.record_anomaly(
                            user_type, 'cloud_game', group_name, f'第{day_idx+1}天',
                            anomaly_display_value, f'低于{self.config.cloud_game_threshold}%阈值',
                            qua_version=rqd_version, specific_date=specific_date
                        )

                    result['cloud_game_rate'][i].append(formatted_rate)


class AdDataProcessor(DataProcessor):
    """广告数据处理器：专门处理广告相关数据"""

    def process_data(self, raw_data: Any, versions: List[str], user_type: str, test_dates: List[str]) -> Any:
        """实现抽象方法 - 处理广告数据"""
        return self.process_ad_data(versions, test_dates, raw_data)

    def process_ad_data(self, qua_versions: List[str], test_dates: List[str], beacon_client, user_type: str = 'unknown') -> Dict[str, List[List[str]]]:
        """处理广告数据"""
        # 过滤掉占位符版本
        valid_qua_versions = [qua for qua in qua_versions if qua != "/"]
        valid_test_dates = [date for date in test_dates if date and date != "/" and date != "none"]

        print(f"  收集广告数据: {len(qua_versions)}个QUA版本")

        if not valid_qua_versions or not valid_test_dates:
            print(f"     没有有效的QUA版本或测试日期，使用默认数据")
            default_matrix = [["/"] * 4 for _ in range(4)]
            return {
                'ad_exposure': [row[:] for row in default_matrix],
                'ad_click': [row[:] for row in default_matrix],
                'ad_download': [row[:] for row in default_matrix],
                'ad_install': [row[:] for row in default_matrix],
                'click_ratio': [row[:] for row in default_matrix],
                'download_ratio': [row[:] for row in default_matrix],
                'install_ratio': [row[:] for row in default_matrix]
            }

        # 添加重试机制
        max_retries = 2
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 格式化日期
                start_date = valid_test_dates[0].replace('/', '-')
                end_date = valid_test_dates[-1].replace('/', '-')

                # 收集广告数据
                print(f"     收集广告数据...")
                ad_data = beacon_client.query_advertisement_data(
                    start_date=start_date,
                    end_date=end_date,
                    version_list=valid_qua_versions,
                    scene_split=False
                )

                # 处理数据
                result = self._process_real_ad_data(ad_data, qua_versions, user_type, test_dates)

                print(f"     广告数据收集完成")
                return result

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"     获取广告数据失败: {e}，正在重试 ({retry_count}/{max_retries})...")
                    time.sleep(5)  # 等待5秒后重试
                else:
                    print(f"     获取广告数据失败: {e}")
                    default_matrix = [["/"] * 4 for _ in range(4)]
                    return {
                        'ad_exposure': [row[:] for row in default_matrix],
                        'ad_click': [row[:] for row in default_matrix],
                        'ad_download': [row[:] for row in default_matrix],
                        'ad_install': [row[:] for row in default_matrix],
                        'click_ratio': [row[:] for row in default_matrix],
                        'download_ratio': [row[:] for row in default_matrix],
                        'install_ratio': [row[:] for row in default_matrix]
                    }

    def _process_real_ad_data(self, ad_data: Dict, qua_versions: List[str], user_type: str, test_dates: List[str]) -> Dict[str, List[List[str]]]:
        """处理真实的广告数据"""
        # 初始化结果结构
        result = {
            'ad_exposure': [[] for _ in range(4)],
            'ad_click': [[] for _ in range(4)],
            'ad_download': [[] for _ in range(4)],
            'ad_install': [[] for _ in range(4)],
            'click_ratio': [[] for _ in range(4)],
            'download_ratio': [[] for _ in range(4)],
            'install_ratio': [[] for _ in range(4)]
        }

        # 处理广告数据
        if ad_data:
            self._process_ad_data(ad_data, qua_versions, result, user_type, test_dates)
        else:
            # 如果没有广告数据，填充默认值
            for i in range(4):
                for key in ['ad_exposure', 'ad_click', 'ad_download', 'ad_install', 'click_ratio', 'download_ratio', 'install_ratio']:
                    result[key][i] = ["/"] * 4

        return result

    def _process_ad_data(self, ad_data: Dict[str, Any], qua_versions: List[str], result: Dict[str, List[List[str]]], user_type: str, test_dates: List[str]) -> None:
        """处理广告数据 - 支持实验组与对照组比较"""
        try:
            # 第一轮：收集所有原始数据
            raw_ad_data = []
            for qua_version in qua_versions:
                qua_raw_data = []
                for date in sorted(ad_data.keys()):
                    if qua_version == "/" or qua_version not in ad_data[date]:
                        qua_raw_data.append(None)
                    else:
                        version_data = ad_data[date][qua_version]
                        qua_raw_data.append({
                            'exposure': version_data.get('广告曝光', 0),
                            'click': version_data.get('广告点击', 0),
                            'download': version_data.get('广告下载', 0),
                            'install': version_data.get('广告安装', 0),
                            'click_ratio': version_data.get('点击曝光率', 0),
                            'download_ratio': version_data.get('下载点击率', 0),
                            'install_ratio': version_data.get('安装下载率', 0)
                        })
                raw_ad_data.append(qua_raw_data)

            # 第二轮：进行实验组与对照组比较，生成最终格式化数据
            self._process_ad_comparison(raw_ad_data, qua_versions, result, sorted(ad_data.keys()), user_type, test_dates)
        except (IndexError, KeyError) as e:
            print(f"     处理广告数据时发生错误: {e}")
            # 填充默认值
            for i in range(4):
                for key in ['ad_exposure', 'ad_click', 'ad_download', 'ad_install', 'click_ratio', 'download_ratio', 'install_ratio']:
                    result[key][i] = ["/"] * 4

    def _process_ad_comparison(self, raw_ad_data: List[List], qua_versions: List[str], result: Dict[str, List[List[str]]], sorted_dates: List[str], user_type: str, test_dates: List[str]) -> None:
        """处理广告数据，进行实验组与对照组比较"""
        # 确定对照组索引（通常是最后一个非占位符版本）
        control_group_index = self._find_control_group_index(qua_versions, raw_ad_data)

        if control_group_index is None:
            # 如果没有对照组，直接格式化所有数据
            for i, qua_data in enumerate(raw_ad_data):
                for day_idx, day_data in enumerate(qua_data):
                    if day_data is None:
                        result['ad_exposure'][i].append("/")
                        result['ad_click'][i].append("/")
                        result['ad_download'][i].append("/")
                        result['ad_install'][i].append("/")
                        result['click_ratio'][i].append("/")
                        result['download_ratio'][i].append("/")
                        result['install_ratio'][i].append("/")
                    else:
                        result['ad_exposure'][i].append(str(day_data['exposure']))
                        result['ad_click'][i].append(str(day_data['click']))
                        result['ad_download'][i].append(str(day_data['download']))
                        result['ad_install'][i].append(str(day_data['install']))
                        result['click_ratio'][i].append(f"{day_data['click_ratio']:.2f}%")
                        result['download_ratio'][i].append(f"{day_data['download_ratio']:.2f}%")
                        result['install_ratio'][i].append(f"{day_data['install_ratio']:.2f}%")
            return

        # 获取对照组数据
        control_group_ad = raw_ad_data[control_group_index]

        # 有对照组的情况下进行完整比较
        for i, qua_data in enumerate(raw_ad_data):
            group_name = f"实验组{i+1}" if i < 3 else "对照组"

            if qua_data is None or all(day is None for day in qua_data):
                # 占位符组
                for _ in range(len(sorted_dates)):
                    result['ad_exposure'][i].append("/")
                    result['ad_click'][i].append("/")
                    result['ad_download'][i].append("/")
                    result['ad_install'][i].append("/")
                    result['click_ratio'][i].append("/")
                    result['download_ratio'][i].append("/")
                    result['install_ratio'][i].append("/")
                continue

            # 处理每天的数据
            for day_idx, (date, day_data) in enumerate(zip(sorted_dates, qua_data)):
                if day_data is None:
                    result['ad_exposure'][i].append("/")
                    result['ad_click'][i].append("/")
                    result['ad_download'][i].append("/")
                    result['ad_install'][i].append("/")
                    result['click_ratio'][i].append("/")
                    result['download_ratio'][i].append("/")
                    result['install_ratio'][i].append("/")
                    continue

                # 基础数据（不进行比较）
                result['ad_exposure'][i].append(str(day_data['exposure']))
                result['ad_click'][i].append(str(day_data['click']))
                result['ad_download'][i].append(str(day_data['download']))
                result['ad_install'][i].append(str(day_data['install']))

                # 转化率数据（进行实验组与对照组比较）
                click_ratio = day_data['click_ratio']
                download_ratio = day_data['download_ratio']
                install_ratio = day_data['install_ratio']

                click_ratio_formatted = f"{click_ratio:.2f}%"
                download_ratio_formatted = f"{download_ratio:.2f}%"
                install_ratio_formatted = f"{install_ratio:.2f}%"

                # 如果是实验组，与对照组比较
                if i != control_group_index and i < 3:  # 实验组（前3组）
                    if (day_idx < len(control_group_ad) and
                        control_group_ad[day_idx] is not None):
                        control_day_data = control_group_ad[day_idx]

                        # 比较点击曝光率（阈值：0.1%）
                        control_click_ratio = control_day_data['click_ratio']
                        click_display_value = float(f"{click_ratio:.2f}")
                        control_click_display_value = float(f"{control_click_ratio:.2f}")
                        click_diff = control_click_display_value - click_display_value
                        if click_diff >= self.config.ad_click_threshold:
                            click_ratio_formatted = f'<span style="color: red;">{click_ratio_formatted}</span>'

                            # 记录异常数据
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            # 获取对应的测试日期
                            specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                            self.anomaly_detector.record_anomaly(
                                user_type, 'ad_click_exposure', group_name, f'第{day_idx+1}天',
                                f'{click_ratio:.2f}%', f'比对照组低{click_diff:.2f}%',
                                qua_version=qua_version, specific_date=specific_date
                            )

                        # 比较下载点击率（阈值：10%）
                        control_download_ratio = control_day_data['download_ratio']
                        download_display_value = float(f"{download_ratio:.2f}")
                        control_download_display_value = float(f"{control_download_ratio:.2f}")
                        download_diff = control_download_display_value - download_display_value
                        if download_diff >= self.config.ad_download_threshold:
                            download_ratio_formatted = f'<span style="color: red;">{download_ratio_formatted}</span>'

                            # 记录异常数据
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            # 获取对应的测试日期
                            specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                            self.anomaly_detector.record_anomaly(
                                user_type, 'ad_download_click', group_name, f'第{day_idx+1}天',
                                f'{download_ratio:.2f}%', f'比对照组低{download_diff:.2f}%',
                                qua_version=qua_version, specific_date=specific_date
                            )

                        # 比较安装下载率（阈值：10%）
                        control_install_ratio = control_day_data['install_ratio']
                        install_display_value = float(f"{install_ratio:.2f}")
                        control_install_display_value = float(f"{control_install_ratio:.2f}")
                        install_diff = control_install_display_value - install_display_value
                        if install_diff >= self.config.ad_install_threshold:
                            install_ratio_formatted = f'<span style="color: red;">{install_ratio_formatted}</span>'

                            # 记录异常数据
                            qua_version = qua_versions[i] if i < len(qua_versions) else "N/A"
                            # 获取对应的测试日期
                            specific_date = test_dates[day_idx] if day_idx < len(test_dates) else "/"
                            self.anomaly_detector.record_anomaly(
                                user_type, 'ad_install_download', group_name, f'第{day_idx+1}天',
                                f'{install_ratio:.2f}%', f'比对照组低{install_diff:.2f}%',
                                qua_version=qua_version, specific_date=specific_date
                            )

                result['click_ratio'][i].append(click_ratio_formatted)
                result['download_ratio'][i].append(download_ratio_formatted)
                result['install_ratio'][i].append(install_ratio_formatted)
