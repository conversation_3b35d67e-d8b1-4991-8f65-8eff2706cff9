from src.client.beacon_client import BeaconClient
from src.client.iwiki_client import IWikiClient
from src.client.tedi_client import TediClient
from src.modules.version_tool.version_info.agent.response_parser_utils import ResponseParserUtils
from typing import Optional, Dict, Any
from src.common.error.detailed_value_error import raise_value_error, ErrorCode, DetailedValueError
from datetime import datetime
from src.modules.version_tool.version_info.agent.rule_engine import RuleEngine
from src.common.logs.loggers.event_logger import event_logger
from src.common.logs.loggers.models import Event
from src.slot_filter.slot_filter import slot_filter
from src.common.models.agent_result import AgentResult

class VersionInfoAgent:
    def __init__(self):
        self._beacon_client = BeaconClient()
        self._iwiki_client = IWikiClient()
        self._tedi_client = TediClient()
        self._rule_engine = RuleEngine()
    
    def process_version_info(self, extracted_params: Dict[str, Any], user_input: str = "") -> AgentResult:
        """
        处理版本信息查询的业务逻辑
        
        Args:
            extracted_params: 从slot_filter提取的参数
            user_input: 原始用户输入（用于日志记录）
            
        Returns:
            AgentResult: 版本信息查询结果
        """
        try:
            scene = extracted_params.get("issue_scene")
            print(f"[版本信息查询]\n场景: {scene}\n参数: {extracted_params}")
            
            # 格式化参数（模糊描述到标准描述的映射）
            formatted_fields = self._rule_engine.format_parameters(extracted_params)
            print(f"[参数格式化]\n参数解析: {formatted_fields}")
            
            # 根据不同场景调用不同的查询函数
            result = None
            if scene == "查询版本覆盖率":
                result = self.query_active_coverage(formatted_fields)
            elif scene == "查询版本需求":
                result = self.query_version_requirements(formatted_fields)
            elif scene == "查询版本计划":
                try:
                    result = self.query_version_plan(formatted_fields)
                    # 记录版本计划查询日志
                    if result:
                        self._log_version_plan_query(user_input, result, extracted_params, formatted_fields)
                except Exception as e:
                    # 记录错误日志
                    error_msg = str(e)
                    if hasattr(e, 'message'):
                        error_msg = e.message
                    self._log_version_plan_query(user_input, error_msg, extracted_params, formatted_fields)
                    raise
            elif scene == "其他":
                raise_value_error(
                    ErrorCode.USER_INTENT_SCENE_NOT_FOUND,
                    message="抱歉，版本信息查询模块目前不支持该查询😥。我可以帮您查询以下信息：\n\n📊 活跃版本覆盖率\n📋 版本需求分组信息\n📅 版本开发计划\n\n请告诉我您想查询哪方面的信息？"
                )
            
            # 返回成功结果
            return AgentResult.success(result)

        except DetailedValueError as e:
            # 处理业务逻辑相关的错误
            error_message = str(e)
            if hasattr(e, 'message'):
                error_message = e.message
            return AgentResult.error(error_message)
        
        except Exception as e:
            # 处理其他未预期的错误
            print(f"Unexpected error: {str(e)}")  # 记录错误日志
            error_msg = "抱歉，大模型服务繁忙，请稍后重试。如果问题持续存在，请联系技术支持。"
            return AgentResult.error(error_msg)

    def query_active_coverage(self, formatted_fields: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询覆盖率（包含活跃覆盖率和联网覆盖率）

        Args:
            formatted_fields: 从用户查询中提取的字段
                - qua_list: QUA列表，多个用逗号分隔
                - app_version: 版本号，多个用逗号分隔
                - start_time: 开始时间，格式：YYYY-MM-DD HH:mm:ss
                - end_time: 结束时间，格式：YYYY-MM-DD HH:mm:ss

        Returns:
            Dict[str, Any]: 查询结果，包含：
                - 数据来源: 固定为"Beacon"
                - 活跃覆盖率信息: 包含查询的活跃版本数据列表
                - 联网覆盖率信息: 包含查询的联网版本数据列表
                    - 版本号: app_version或qua
                    - 活跃用户数: dau
                    - 覆盖率: coverage_rate
        """

        # 从extracted_fields中获取查询参数
        qua_list = formatted_fields.get("qua_list")
        app_version = formatted_fields.get("app_version")
        start_time = formatted_fields.get("start_time")
        end_time = formatted_fields.get("end_time")

        # 参数校验
        if not (app_version or qua_list):
            raise_value_error(
                ErrorCode.MISSING_REQUIRED_FIELD,
                message="请提供app版本号或qua列表"
            )

        # 时间补全逻辑
        if start_time and not end_time:
            # 如果只有开始时间，结束时间设为当前时间
            end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        elif end_time and not start_time:
            # 如果只有结束时间，开始时间设为2023年1月1日
            start_time = "2023-01-01 00:00:00"

        try:
            # 处理 qua_list：支持单个或多个，逗号分隔
            processed_qua_list = None
            if qua_list:
                processed_qua_list = [qua.strip() for qua in qua_list.split(",")]

            # 处理 app_version：支持单个或多个，逗号分隔
            processed_app_version = None
            if app_version:
                if "," in str(app_version):
                    processed_app_version = [v.strip() for v in str(app_version).split(",")]
                else:
                    processed_app_version = app_version

            # 同时查询活跃覆盖率和联网覆盖率
            active_result = self._beacon_client.query_active_coverage(
                qua_list=processed_qua_list,
                app_version=processed_app_version,
                start_time=start_time,
                end_time=end_time
            )

            network_result = self._beacon_client.query_network_coverage(
                qua_list=processed_qua_list,
                app_version=processed_app_version,
                start_time=start_time,
                end_time=end_time
            )

            # 转换为中文格式的输出
            formatted_result = {
                "数据来源": "Beacon",
                "活跃覆盖率信息": [],
                "联网覆盖率信息": []
            }

            # 添加时间范围信息（如果有）
            if start_time and end_time:
                formatted_result["查询时间"] = f"{start_time} 至 {end_time}"

            # 格式化活跃覆盖率数据
            for item in active_result:
                formatted_result["活跃覆盖率信息"].append({
                    "版本号": item["app_version"],
                    "活跃用户数": item["dau"],
                    "覆盖率": item["coverage_rate"]
                })

            # 格式化联网覆盖率数据
            for item in network_result:
                formatted_result["联网覆盖率信息"].append({
                    "版本号": item["app_version"],
                    "联网用户数": item["dau"],
                    "覆盖率": item["coverage_rate"]
                })

            return ResponseParserUtils.format_coverage(formatted_result)

        except DetailedValueError:
            raise
        except Exception as e:
            raise_value_error(
                ErrorCode.BEACON_API_ERROR,
                message=f"查询覆盖率失败,错误信息: {str(e)}"
            )

    def query_online_coverage(self, formatted_fields: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询在线覆盖率
        """
        pass

    def query_version_requirements(self, formatted_fields: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询版本需求

        Args:
            formatted_fields: 从用户查询中提取的字段
                - app_version: 版本号，必填
                - dev: 开发人员名称，可选

        Returns:
            Dict[str, Any]: 查询结果，包含：
                - 数据来源: 固定为"IWiki"
                - 版本号: 查询的版本号
                - 需求列表: 包含查询到的需求信息列表
                    - 研发: 开发人员名称
                    - 需求: 需求描述
                    - 实验: 实验名称
                    - 新用户灰度分支: 新用户灰度分支名称
                    - 旧用户灰度分支: 旧用户灰度分支名称
                - 需求表链接: 版本需求表的链接

        Raises:
            DetailedValueError: 当缺少必要参数或查询失败时抛出
        """
        # 从extracted_fields中获取查询参数
        app_version = formatted_fields.get("app_version")
        dev = formatted_fields.get("node_name")

        # 参数校验
        if not app_version:
            raise_value_error(
                ErrorCode.MISSING_REQUIRED_FIELD,
                message="请在询问时提供需要查询的版本号。"
            )

        try:
            # 调用iwiki client查询版本需求
            requirements_result = self._iwiki_client.get_version_requirements(
                version=app_version,
                dev=dev
            )

            # 转换为标准输出格式
            formatted_result = {
                "数据来源": "IWiki",
                "版本号": app_version,
                "需求列表": requirements_result['requirements'],
                "需求表链接": requirements_result['requirements_url']
            }

            return ResponseParserUtils.format_requirements(formatted_result)

        except DetailedValueError:
            raise
        except Exception as e:
            raise_value_error(
                ErrorCode.IWIKI_API_ERROR,
                message=f"查询版本需求失败,错误信息: {str(e)}"
            )

    def query_version_plan(self, formatted_fields: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询版本计划

        Args:
            formatted_fields: 从用户查询中提取的字段
                - app_version: 版本号，必填
                - app_version_hint: 版本号描述，可选
                - node_name: 节点名称，可选

        Returns:
            Dict[str, Any]: 查询结果，包含：
                - 当只有app_version时：返回该版本下的所有计划信息
                - 当有app_version和node_name时：返回指定节点的计划信息
                - 当node_name为"灰度实验"时：返回灰度实验的时间范围

        Raises:
            DetailedValueError: 当缺少必要参数或查询失败时抛出
        """
        # 从extracted_fields中获取查询参数
        app_version = formatted_fields.get("app_version")
        app_version_hint = formatted_fields.get("app_version_hint")
        node_name = formatted_fields.get("node_name")

        # 参数校验
        if not app_version:
            raise_value_error(
                ErrorCode.MISSING_REQUIRED_FIELD,
                message="请告诉我具体要查询的版本号，或者如果要查询当前/下一个/上一个版本，请告诉我:【版本查询】查询当前/下一个/上一个版本计划。"
            )

        try:
            # 情况1：如果只有app_version,根据app_version_hint映射到不同接口查询
            if not node_name:
                # 如果为当前版本，调用query_current_plan
                if app_version_hint == "current":
                    return self._query_current_plan(formatted_fields)
                # 如果为历史版本，从tedi查询
                elif app_version_hint == "pre" or app_version_hint == "history":
                    raw_data, processed_data = self._tedi_client.get_version_workflow_detail(app_version)
                    data_dict = {
                        "数据来源": "Tedi",
                        "版本号": app_version,
                        "该版本下的所有计划": processed_data
                    }
                    return ResponseParserUtils.format_plan_data_tree(data_dict)
                # 如果为下一个或未来版本，从iwiki查询
                elif app_version_hint == "next" or app_version_hint == "future":
                    data = self._iwiki_client.get_version_calendar(app_version)
                    data_dict = {
                        "数据来源": "IWiki",
                        "版本号": app_version,
                        "该版本下的所有计划": data
                    }
                    return ResponseParserUtils.format_calendar(data_dict)
            
            # 情况2：有app_version、node_name，且node_name为"灰度实验"，查询灰度实验时间范围
            elif node_name == "灰度实验":
                if app_version_hint == "current" or app_version_hint == "pre" or app_version_hint == "history":
                    data_dict = self._query_gray_experiment_plan_tedi(app_version)
                    return ResponseParserUtils.format_plan_node(data_dict)
                elif app_version_hint == "next" or app_version_hint == "future":
                    data_dict = self._query_gray_experiment_plan_iwiki(app_version)
                    return ResponseParserUtils.format_plan_node(data_dict)
                   
            # 情况3：有app_version和node_name
            else:
                # 已存在的版本查询tedi，先查询stage，再查询step
                if app_version_hint == "current" or app_version_hint == "pre" or app_version_hint == "history":
                    try:
                        data_dict = self._query_node_plan_info_tedi(app_version, node_name)
                        
                        # 检查节点状态，如果是节点状态是WAITING，转向查询日历
                        if data_dict.get("计划信息", {}).get("status") == "WAITING":
                            try:
                                # 使用rule_engine的format_iwiki_node_name方法将节点名称映射为iwiki格式
                                iwiki_node_name = self._rule_engine.format_iwiki_node_name(node_name)
                                data = self._iwiki_client.get_version_calendar(app_version, iwiki_node_name)
                                data_dict = {
                                    "数据来源": "IWiki",
                                    "版本号": app_version,
                                    "节点信息": data
                                }
                                return ResponseParserUtils.format_calendar(data_dict)
                            except Exception as calendar_error:
                                # 如果日历查询失败，返回原始tedi结果
                                pass
                        
                        return ResponseParserUtils.format_plan_node(data_dict)
                    except DetailedValueError as tedi_error:
                        # 如果在tedi中找不到节点，尝试在日历中查找
                        try:
                            # 使用rule_engine的format_iwiki_node_name方法将节点名称映射为iwiki格式
                            iwiki_node_name = self._rule_engine.format_iwiki_node_name(node_name)
                            data = self._iwiki_client.get_version_calendar(app_version, iwiki_node_name)
                            data_dict = {
                                "数据来源": "IWiki",
                                "版本号": app_version,
                                "节点信息": data
                            }
                            return ResponseParserUtils.format_calendar(data_dict)
                        except Exception as calendar_error:
                            # 如果日历查询也失败，抛出原始的tedi错误
                            raise tedi_error
                        
                # 未来版本查询iwiki
                elif app_version_hint == "next" or app_version_hint == "future":
                    data = self._iwiki_client.get_version_calendar(app_version, node_name)
                    print(f"data: {data}")
                    data_dict = {
                        "数据来源": "IWiki",
                        "版本号": app_version,
                        "节点信息": data
                    }
                    return ResponseParserUtils.format_calendar(data_dict)

        except DetailedValueError:
            raise
        except Exception as e:
            raise_value_error(
                ErrorCode.TEDI_API_ERROR,
                message=f"查询版本计划失败,错误信息: {str(e)}"
            )

    def _log_version_plan_query(self, user_input: str, result: str, extracted_fields: Dict[str, Any], formatted_fields: Dict[str, Any]):
        """
        记录版本计划查询事件日志
        
        Args:
            user_input: 用户原始输入
            result: 查询结果字符串
            extracted_fields: 从用户查询中提取的原始字段
            formatted_fields: 格式化后的查询参数
        """
        try:
            now = datetime.now()
            event = Event(
                timestamp=now.strftime("%Y%m%d-%H%M%S"),
                event_type="version_plan_query",
                event_params={
                    "user_input": user_input,
                    "query_result": result,
                    "extracted_fields": extracted_fields,
                    "formatted_fields": formatted_fields
                }
            )
            event_logger.write_event(event)
        except Exception as e:
            # 日志记录失败不应该影响主业务流程
            print(f"Failed to log version plan query event: {str(e)}")

    def _query_gray_experiment_plan_tedi(self, app_version: str) -> Dict[str, Any]:
        """
        查询灰度实验计划时间范围

        Args:
            app_version: 版本号

        Returns:
            Dict[str, Any]: 灰度实验计划信息，包含开始时间和结束时间
        """
        try:
            # 1. 查询"版本灰度准备"的开始时间和状态
            start_time = None
            start_status = None
            try:
                result = self._tedi_client.get_stageOrStep_info(
                    version=app_version,
                    stage_name="版本灰度准备"
                )
                if 'stage' in result:
                    start_time = result['stage'].get('startTime')
                    start_status = result['stage'].get('status')
            except Exception:
                pass  # 如果查询失败，继续查询其他阶段

            # 2. 如果开始节点状态是WAITING，转而查询日历信息
            if start_status == "WAITING":
                try:
                    data_dict = self._query_gray_experiment_plan_iwiki(app_version)
                    return data_dict
                except Exception as calendar_error:
                    # 如果日历查询失败，继续使用tedi的逻辑
                    pass

            # 3. 查询各次灰度的结束时间，找到最后一次
            end_time = None
            gray_stages = ["第一次灰度", "第二次灰度", "第三次灰度", "第四次灰度", "第五次灰度"]
            
            for stage_name in gray_stages:
                try:
                    result = self._tedi_client.get_stageOrStep_info(
                        version=app_version,
                        stage_name=stage_name
                    )
                    if 'stage' in result and result['stage'].get('endTime'):
                        end_time = result['stage']['endTime']
                except Exception:
                    continue  # 如果某个阶段查询失败，继续查询下一个

            # 4. 如果都没有找到，抛出异常
            if not start_time and not end_time:
                raise_value_error(
                    ErrorCode.TEDI_API_ERROR,
                    message=f"版本 {app_version} 的灰度实验计划未制定"
                )

            # 5. 返回灰度实验时间范围
            gray_experiment_info = {}
            gray_experiment_info["name"] = "灰度实验"
            if start_time:
                gray_experiment_info["startTime"] = start_time
            if end_time:
                gray_experiment_info["endTime"] = end_time            

            return {
                "数据来源": "Tedi",
                "版本号": app_version,
                "节点": "灰度实验",
                "计划信息": gray_experiment_info
            }

        except DetailedValueError:
            raise
        except Exception as e:
            raise_value_error(
                ErrorCode.TEDI_API_ERROR,
                message=f"查询灰度实验计划失败,错误信息: {str(e)}"
            )

    def _query_gray_experiment_plan_iwiki(self, app_version: str) -> Dict[str, Any]:
        """
        查询灰度实验计划时间范围（从IWiki）

        Args:
            app_version: 版本号

        Returns:
            Dict[str, Any]: 灰度实验计划信息，包含开始时间和结束时间
        """
        try:
            # 调用iwiki client获取版本日历数据
            calendar_data = self._iwiki_client.get_version_calendar(app_version)
            
            # 从日历数据中筛选出"实验灰度"节点
            gray_experiment_data = None
            for item in calendar_data:
                if "实验灰度" in item.get("node", ""):
                    gray_experiment_data = item
                    break
            
            # 如果没有找到实验灰度节点，抛出异常
            if not gray_experiment_data:
                raise_value_error(
                    ErrorCode.IWIKI_API_ERROR,
                    message=f"版本 {app_version} 的灰度实验计划未制定"
                )
            
            # 构造灰度实验信息
            gray_experiment_info = {
                "name": "灰度实验",
                "startTime": gray_experiment_data.get("start_time"),
                "endTime": gray_experiment_data.get("end_time")
            }
            
            return {
                "数据来源": "IWiki",
                "版本号": app_version,
                "节点": "灰度实验",
                "计划信息": gray_experiment_info
            }

        except DetailedValueError:
            raise
        except Exception as e:
            raise_value_error(
                ErrorCode.IWIKI_API_ERROR,
                message=f"查询灰度实验计划失败,错误信息: {str(e)}"
            )

    def _query_current_plan(self, extracted_fields: Dict[str, Any]) -> Dict[str, Any]:
        """
        查询当前版本计划

        Args:
            extracted_fields: 从用户查询中提取的字段
                - node_name: 节点名称，可选

        Returns:
            Dict[str, Any]: 查询结果，包含：
                - 当没有node_name时：返回所有正在运行的版本信息
                - 当有node_name时：返回当前版本中指定节点的计划信息

        Raises:
            DetailedValueError: 当查询失败时抛出
        """
        # 从extracted_fields中获取查询参数
        node_name = extracted_fields.get("node_name")

        try:
            # 获取正在运行的版本信息
            running_versions = self._tedi_client.get_running_versions_info()
            
            # 初始化app_version和version_num变量
            app_version = None # 版本号如9.0.0
            version_num = None # 版本号数字如900
            
            # 如果没有运行版本，查询最新版本
            if not running_versions:
                # 获取版本列表
                version_list = self._tedi_client.get_version_list()
                if not version_list:
                    raise_value_error(
                        ErrorCode.TEDI_API_ERROR,
                        message="当前没有正在运行的版本，且无法获取版本列表"
                    )
                
                # 获取第一个版本（最新版本）
                latest_version_info = version_list[0]
                app_version = latest_version_info.get("appVersion")
                if not app_version:
                    raise_value_error(
                        ErrorCode.TEDI_API_ERROR,
                        message="无法获取最新版本的版本号"
                    )
            else:
                # 获取第一个正在运行的版本（通常是最新的）
                current_version_info = running_versions[0]
                app_version = current_version_info.get("appVersion")
                if not app_version:
                    raise_value_error(
                        ErrorCode.TEDI_API_ERROR,
                        message="无法获取当前版本的版本号"
                    )
            
            # 从appVersion中提取数字版本号
            version_num = ''.join(filter(str.isdigit, app_version))
            if not version_num:
                raise_value_error(
                    ErrorCode.TEDI_API_ERROR,
                    message=f"无法从版本号 {app_version} 中提取数字版本号"
                )
            
            # 如果没有运行版本且没有node_name，使用get_version_workflow_detail获取最新版本的详细信息
            if not running_versions and not node_name:
                raw_data, processed_data = self._tedi_client.get_version_workflow_detail(version_num)
                data_dict = {
                    "数据来源": "Tedi",
                    "版本号": version_num,
                    "该版本下的所有计划": processed_data
                }
                return ResponseParserUtils.format_plan_data_tree(data_dict)

            # 如果有当前运行版本，没有node_name，返回所有正在运行的版本信息
            if not node_name:
                data_dict = {
                    "数据来源": "Tedi",
                    "当前版本计划": running_versions
                }
                return ResponseParserUtils.format_current_version_plan(data_dict)
            # 情况2：有node_name，查询当前版本中指定节点的计划信息
            else:
                # 使用辅助方法查询指定节点的计划信息
                data_dict = self._query_node_plan_info_tedi(version_num, node_name)
                return ResponseParserUtils.format_plan_node(data_dict)

        except DetailedValueError:
            raise
        except Exception as e:
            raise_value_error(
                ErrorCode.TEDI_API_ERROR,
                message=f"查询当前版本计划失败,错误信息: {str(e)}"
            )

    def _query_node_plan_info_tedi(self, app_version: str, node_name: str) -> Dict[str, Any]:
        """
        从tedi查询指定版本中指定节点的计划信息（辅助方法，复用stage/step查询逻辑）

        Args:
            app_version: 版本号
            node_name: 节点名称

        Returns:
            Dict[str, Any]: 查询结果

        Raises:
            DetailedValueError: 当查询失败时抛出
        """

        # 先尝试作为stage_name查询
        try:
            result = self._tedi_client.get_stageOrStep_info(
                version=app_version,
                stage_name=node_name
            )
            if 'stage' in result:
                return {
                    "数据来源": "Tedi",
                    "版本号": app_version,
                    "节点": node_name,
                    "计划信息": result['stage']
                }
        except Exception as stage_error:
            # stage查询失败，继续尝试step查询
            pass

        # 如果stage查询失败或没有找到，尝试作为step_name查询
        try:
            result = self._tedi_client.get_stageOrStep_info(
                version=app_version,
                step_name=node_name
            )
            if 'step' in result:
                return {
                    "数据来源": "Tedi",
                    "版本号": app_version,
                    "节点": node_name,
                    "计划信息": result['step']
                }
        except Exception as step_error:
            # step查询也失败，抛出错误
            pass

        # 如果stage和step都没有找到，抛出错误
        raise_value_error(
            ErrorCode.TEDI_API_ERROR,
            message=f"未找到版本 {app_version} 中名为 '{node_name}' 的节点或步骤。请检查您的版本号或节点名称是否正确。",
            context={"app_version": app_version, "node_name": node_name}
        )


if __name__ == '__main__':
    # 创建 VersionInfoAgent 实例
    bot = VersionInfoAgent()

    # 测试用例
    test_coverage_queries = [
        # 测试单个版本号查询
        "查询898版本的活跃覆盖率",
        # 测试多个版本号查询
        "查询898,899,900版本的活跃覆盖率",
        # 测试单个qua查询
        "查询TMAF_858_P_9521的活跃覆盖率",
        # 测试多个qua查询
        "查询TMAF_858_P_9521、TMAF_858_P_9522的活跃覆盖率",
        # 测试版本号+包名查询
        "查询898,TMAF_858_P_9521的活跃覆盖率",
        # 测试未输入包名的场景
        "活跃覆盖率是多少",
        # 测试无法识别的场景
        "你好",
    ]

    test_version_requirements_queries = [
        # 正常场景（目前只有889版本）
        "查询889版本下我的需求"
    ]

    test_version_plan_queries = [
        # 正常场景
        # 测试查询版本的所有计划
        ## hint = current
        "查询当前版本计划",
        ## hint = pre
        "查询上一个版本计划",
        ## hint = next
        "查询下一个版本计划",
        ## hint = history
        "查询899版本计划",
        ## hint = future
        "查询901版本计划",
        # 测试版本节点信息
        ## 测试灰度实验信息
        ### hint = current、pre、history
        "查询当前版本的灰度实验",
        "查询上一个版本的灰度",
        "查询下一版本灰度",        
        "查询899版本的灰度实验",
        ## hint = next、future
        "查询下一个版本的灰度实验",
        "查询901版本的灰度时间",
        ## 测试节点信息
        ### hint = current、pre、history
        "查询当前版本的合流截止时间",
        "查询当前版本的小灰计划",
        "查询上一个版本的灰度上线前验证时间",
        "查询下一版本的版本发布完成时间",
        "查询下一版本的不存在节点时间",
        "查询899版本的全量时间",
        ## hint = next、future
        "查询下一个版本的集成测试时间",
        "查询901版本的版本发布完成时间",

        # 异常场景
        ## 不存在的版本计划
        "999版本的计划"

    ]

    test_current_plan_queries = [
        # 测试查询当前版本计划（无节点名）
        "查询当前版本计划",
        # 测试查询当前版本的特定节点计划
        "查询当前版本的版本发布完成时间",
        "查询当前版本的第二次灰度",
        "查询当前版本的待配置灰度",
    ]

    test_queries = [
        # "查询我的需求。",
        # "查询899版本的灰度",
        # "查询目前版本灰度",
        # "899版本覆盖率",
        # "查询901的灰度实验",
        # "查询900的灰度实验",
        # "查询灰度实验"
        # "查询当前版本的灰度实验",
        # "查询下一版本的灰度实验",
        # "查询当前版本的集成测试时间",
        # "查询901的集成测试时间",
        # "查询当前版本信息",
        # "查询当前版本的实验灰度信息",
        # "查询当前版本的集成前准备时间",
        # "900的第一次灰度时间",
        "查询当前版本的版本发布节点的起止时间"

    ]

    print("开始测试查询功能...")
    print("-" * 50)

    for query in test_queries:
        print(f"\n[测试查询]: \n{query}")
        try:
            result = bot.process_version_info(query)
            print(f"[查询结果]: \n{result}")
        except Exception as e:
            print(f"[查询出错]: \n{str(e)}")
        print("-" * 50)
