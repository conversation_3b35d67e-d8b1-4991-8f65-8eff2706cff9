from typing import Dict, Any

class ResponseParserUtils:

    @staticmethod
    def format_coverage(data: Dict[str, Any]) -> str:
        lines = [
            f"📊 数据来源: {data.get('数据来源', '')}",
            ""
        ]
        
        # 处理活跃覆盖率信息
        active_versions = data.get('活跃覆盖率信息', [])
        if active_versions:
            lines.append("📈 活跃覆盖率信息:")
            for v in active_versions:
                lines.append(f"    版本号: {v.get('版本号', '')} | 活跃用户数: {v.get('活跃用户数', '')} | 覆盖率: {v.get('覆盖率', '')}")
            lines.append("")
        
        # 处理联网覆盖率信息
        network_versions = data.get('联网覆盖率信息', [])
        if network_versions:
            lines.append("📈 联网覆盖率信息:")
            for v in network_versions:
                lines.append(f"    版本号: {v.get('版本号', '')} | 联网用户数: {v.get('联网用户数', '')} | 覆盖率: {v.get('覆盖率', '')}")
            lines.append("")
        
        # 处理旧格式兼容性（版本信息字段）
        # versions = data.get('版本信息', [])
        # if versions and not active_versions and not network_versions:
        #     lines.append("📈 版本覆盖率信息:")
        #     for v in versions:
        #         lines.append(f"    版本号: {v.get('版本号', '')} | 活跃用户数: {v.get('活跃用户数', '')} | 覆盖率: {v.get('覆盖率', '')}")
        
        # 如果没有任何数据
        if not active_versions and not network_versions:
            return '\n'.join(lines) + '\n无版本信息'
        
        return '\n'.join(lines).rstrip()

    @staticmethod
    def format_requirements(data: Dict[str, Any]) -> str:
        lines = [
            f"📊 数据来源: {data.get('数据来源', '')}",
            f"🏷️ 版本号: {data.get('版本号', '')}",
            ""
        ]
        reqs = data.get('需求列表', [])
        if not reqs:
            return '\n'.join(lines) + '\n❌ 无需求列表'
        
        lines.append("📋 需求列表")
        
        for i, r in enumerate(reqs, 1):
            lines.append(f"  {i}. 研发: {r.get('研发', '')} | 需求: {r.get('需求', '')} | 实验: {r.get('实验', '')}")
            lines.append(f"     新用户灰度分支: {r.get('新用户灰度分支', '')}")
            lines.append(f"     旧用户灰度分支: {r.get('旧用户灰度分支', '')}")
        
        requirements_url = data.get('需求表链接')
        if requirements_url:
            lines.append("\n")
            lines.append("-" * 40)
            lines.append("ℹ️ 提示信息: 点击链接查看更详细的需求信息")
            lines.append("🔗 详细需求表链接:")
            lines.append(f"[{requirements_url}]({requirements_url})")
        
        return '\n'.join(lines)

    @staticmethod
    def _format_plan_tree(plan: dict, prefix: str = '', is_last: bool = True, include_steps: bool = True) -> str:
        """
        格式化版本计划数据为树形结构递归函数
        Args:
            plan: 版本计划数据
            prefix: 前缀
            is_last: 是否是最后一个
            include_steps: 是否包含步骤信息
        Returns:
            str: 树形结构字符串
        """
        name = plan.get('name', '')
        start = plan.get('startTime', '无')
        end = plan.get('endTime', '无')
        status = plan.get('status', '')
        branch = '└─ ' if is_last else '├─ '
        line = f"{prefix}{branch}名称: {name} | 开始时间: {start} | 结束时间: {end} | 状态: {status}"
        lines = [line]

        if is_last:
            child_prefix = prefix + '   '
        else:
            child_prefix = prefix + '│  '

        if include_steps:
            steps = plan.get('steps', [])
            if steps:
                lines.append(f"{child_prefix}步骤:")
                for i, step in enumerate(steps):
                    last = (i == len(steps) - 1) and not plan.get('nextSteps') and not plan.get('nextStages')
                    lines.append(ResponseParserUtils._format_plan_tree(step, child_prefix + '  ', last, include_steps))

            next_steps = plan.get('nextSteps', [])
            if next_steps:
                lines.append(f"{child_prefix}后续步骤:")
                for i, next_step in enumerate(next_steps):
                    last = (i == len(next_steps) - 1) and not plan.get('nextStages')
                    lines.append(ResponseParserUtils._format_plan_tree(next_step, child_prefix + '  ', last, include_steps))

        next_stages = plan.get('nextStages', [])
        if next_stages:
            lines.append(f"{child_prefix}后续阶段:")
            for i, next_stage in enumerate(next_stages):
                last = (i == len(next_stages) - 1)
                lines.append(ResponseParserUtils._format_plan_tree(next_stage, child_prefix + '  ', last, include_steps))

        return '\n'.join(lines)

    @staticmethod
    def format_plan_data_tree(data: dict, include_steps: bool = False) -> str:
        """
        格式化版本计划数据为树形结构
        Args:
            data: 版本计划数据
            include_steps: 是否包含步骤信息，默认包含
        Returns:
            str: 树形结构字符串
        """
        lines = [
            f"📊 数据来源: {data.get('数据来源', '')}",
            f"🏷️ 版本号: {data.get('版本号', '')}",
            ""
        ]

        all_plans = data.get('该版本下的所有计划', {})
        lines.append(f"计划ID: {all_plans.get('id', '')}")
        lines.append(f"项目名称: {all_plans.get('projectName', '')}")
        status = all_plans.get('status', '')
        status_emoji = "✅" if status == "FINISH" else "🔄" if status == "RUNNING" else "⏳" if status == "WAITING" else "❌"
        lines.append(f"状态: {status_emoji} {status}")

        lines.append('')

        stages = all_plans.get('stages', [])
        for i, stage in enumerate(stages):
            lines.append("阶段:")
            last = (i == len(stages) - 1)
            lines.append(ResponseParserUtils._format_plan_tree(stage, '', last, include_steps))
            lines.append('-' * 50)

        if not include_steps:
            # TODO: 需要优化，目前仅输出阶段信息，不包含步骤信息, 后续查看是否可以在同一对话中支持用户查看步骤信息
            all_plans_website_url = all_plans.get('website_url', '')
            lines.append("\n")
            lines.append("-" * 40)
            lines.append("ℹ️ 提示信息:")
            lines.append(f'当前仅输出阶段信息，不包含步骤信息。如需查看更详细的步骤信息, 请点击以下链接: [{all_plans_website_url}]({all_plans_website_url})')

        return '\n'.join(lines)
    
    @staticmethod
    def format_plan_node(data: Dict[str, Any]) -> str:
        lines = [
            f"📊 数据来源: {data.get('数据来源', '')}",
            f"🏷️ 版本号: {data.get('版本号', '')}",
            ""
        ]
        
        plan_info = data.get('计划信息', {})
        node_name = plan_info.get('name') 
        
        lines.append(f"📋 节点信息")
        lines.append(f"节点名称: {node_name}")
        
        # 确保即使值为空字符串也显示默认值
        start_time = plan_info.get('startTime')
        end_time = plan_info.get('endTime')
        
        if 'status' in plan_info:
            status = plan_info.get('status')
            status_emoji = "✅" if status == "FINISH" else "🔄" if status == "RUNNING" else "⏳" if status == "WAITING" else "❌"
            lines.append(f"状态: {status_emoji} {status}")
            
        lines.append(f"⏰ 开始时间: {start_time if start_time else '⏰ 未设置'}")
        lines.append(f"⏰ 结束时间: {end_time if end_time else '⏰ 未设置'}")
        
        return '\n'.join(lines)

    @staticmethod
    def format_current_version_plan(data):
        lines = [
            f"📊 数据来源: {data.get('数据来源', '')}",
            ""
        ]
        plans = data.get('当前版本计划', [])
        if not plans:
            return '\n'.join(lines) + '\n❌ 无当前版本计划数据'
    
        lines.append("🚀 当前正在进行的版本计划列表")
        lines.append("=" * 60)
        
        for i, plan in enumerate(plans, 1):
            lines.append(f"📦 计划 {i}")
            lines.append("-" * 40)
            lines.append(f"🏷️ 版本号: {plan.get('appVersion', '')}")
            
            # 格式化时间，将T替换为空格
            merge_cutoff = plan.get('合流截止', '').replace('T', ' ') if plan.get('合流截止') else ''
            first_gray = plan.get('一灰', '').replace('T', ' ') if plan.get('一灰') else ''
            full_release = plan.get('全量', '').replace('T', ' ') if plan.get('全量') else ''
            
            lines.append("")
            lines.append("⏰ 时间安排:")
            lines.append(f"    • 合流截止: {merge_cutoff if merge_cutoff else '⏰ 未设置'}")
            lines.append(f"    • 第一次灰度: {first_gray if first_gray else '⏰ 未设置'}")
            lines.append(f"    • 全量同步: {full_release if full_release else '⏰ 未设置'}")
            
            lines.append("")
            lines.append("📍 当前状态:")
            lines.append(f"    • 当前阶段: {plan.get('currentStage', '')}")
            lines.append(f"    • 当前步骤: {plan.get('currentStep', '')}")
            
            # 提取并格式化负责人信息
            current_operators = plan.get('currentOperators', [])
            lines.append("")
            lines.append("👥 当前步骤负责人信息:")
            if current_operators:
                for operator in current_operators:
                    operator_name = operator.get('operator', '')
                    operator_type = operator.get('type', '')
                    operator_status = operator.get('status', '')
                    operator_time = operator.get('time', '')
                    
                    # 格式化负责人信息（中文标识）
                    if operator_time:
                        lines.append(f"    • 负责人: {operator_name} | 职位: {operator_type} | 状态: {operator_status} | 操作时间: {operator_time}")
                    else:
                        lines.append(f"    • 负责人: {operator_name} | 职位: {operator_type} | 状态: {operator_status}")
            else:
                lines.append("  • 暂无负责人信息")

                
        return '\n'.join(lines)

    @staticmethod
    def format_calendar(data: Dict[str, Any]) -> str:
        """
        格式化日历数据为易读的字符串格式
        
        Args:
            data: 版本日历数据
        
        Returns:
            str: 格式化后的日历字符串
        """
        lines = [
            f"📊 数据来源: {data.get('数据来源', '')}",
            f"🏷️ 版本号: {data.get('版本号', '')}",
            ""
        ]
        
        calendar_items = data.get('该版本下的所有计划', [])
        if calendar_items:
            lines.append("📋 版本计划日历")
        else:
            calendar_items = data.get('节点信息', [])
            if calendar_items:
                lines.append("📋 节点信息日历")
            else:
                return '\n'.join(lines) + '\n❌ 无日历计划数据'
        
        lines.append("")
        
        # 按时间排序
        sorted_items = sorted(calendar_items, key=lambda x: x.get('start_time', ''))
        
        current_date = None
        for item in sorted_items:
            node_name = item.get('node', '')
            start_time = item.get('start_time', '')
            end_time = item.get('end_time', '')
            
            # 如果开始日期变化，添加日期分隔符
            if start_time != current_date:
                if current_date is not None:
                    lines.append("")
                current_date = start_time
                lines.append(f"📅 {start_time}:")
            
            # 格式化节点信息
            if start_time == end_time:
                # 单日计划
                lines.append(f"  • {node_name}")
            else:
                # 多日计划
                lines.append(f"  • {node_name} ({start_time} 至 {end_time})")
        
        # 添加版本日历链接
        lines.append("\n")
        lines.append("-" * 40)
        lines.append("ℹ️ 提示信息: 点击链接查看更详细的版本计划信息")
        lines.append("🔗 详细版本日历链接:")
        lines.append("[https://doc.weixin.qq.com/sheet/e3_AEYAPgbdAFwJB1C92TSRwCKppAAky?scode=AJEAIQdfAAoMi4suV9AbcANgbzADM&tab=BB08J2](https://doc.weixin.qq.com/sheet/e3_AEYAPgbdAFwJB1C92TSRwCKppAAky?scode=AJEAIQdfAAoMi4suV9AbcANgbzADM&tab=BB08J2)")
        
        
        return '\n'.join(lines)

