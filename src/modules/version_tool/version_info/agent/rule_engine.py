from datetime import datetime, timedelta
from src.client.tedi_client import TediClient
import re

class RuleEngine:
    def __init__(self):
        # 初始化 current_versions 为空列表
        self.current_versions = []
        
        # 从 TediClient 获取当前运行版本
        try:
            tedi_client = TediClient()
            current_versions = tedi_client.get_running_versions_info()
            if current_versions:
                # 提取所有运行版本的版本号
                for version_info in current_versions:
                    app_version = version_info.get("appVersion")
                    if app_version:
                        # 从 appVersion 中提取数字版本号（去掉可能的点号）
                        # 例如：从 "9.0.0" 提取 "900"
                        version_num = ''.join(filter(str.isdigit, app_version))
                        self.current_versions.append(version_num)
            else:
                # 如果没有运行版本，从版本列表中获取最新版本作为当前版本
                version_list = tedi_client.get_version_list()
                if version_list:
                    # 获取第一个版本（最新版本）
                    latest_version_info = version_list[0]
                    app_version = latest_version_info.get("appVersion")
                    if app_version:
                        # 从 appVersion 中提取数字版本号
                        version_num = ''.join(filter(str.isdigit, app_version))
                        self.current_versions.append(version_num)
                        print(f"没有运行版本，使用最新版本作为当前版本: {version_num}")
        except Exception as e:
            # 如果获取版本信息失败，保持 current_versions 为空列表
            print(f"获取版本信息失败: {str(e)}")
            pass


    def format_parameters(self, fields: dict) -> dict:
        # 复制一份，避免修改原始数据
        std_fields = fields.copy()

        # 1、app_version_hint -> app_version_hint、app_version（仅在 app_version 为 None 时）
        if std_fields.get("app_version") is None and std_fields.get("app_version_hint") is not None:
            # 先映射 app_version_hint
            mapped_hint = self.map_versionhint_to_versionhint(std_fields["app_version_hint"])
            std_fields["app_version_hint"] = mapped_hint
            
            # 根据映射后的 app_version_hint 设置 app_version
            if mapped_hint in ["pre", "current", "next"]:
                app_version = self.get_app_version_from_hint(mapped_hint)
                if app_version is not None:
                    std_fields["app_version"] = app_version

        # 2、app_version -> app_version_hint：如果 app_version 不为 None，映射到app_version_hint
        if std_fields.get("app_version") is not None:
            # 格式化 app_version 为纯数字格式（例如：9.0.1 -> 901）
            app_version = std_fields["app_version"]
            formatted_version = ''.join(filter(str.isdigit, app_version))
            std_fields["app_version"] = formatted_version
            # 映射
            version_type = self.get_version_type(std_fields["app_version"])
            if version_type is not None:
                std_fields["app_version_hint"] = version_type

        # 3、时间范围映射：time_scope_hint -> app_version_hint
        if std_fields.get("time_scope_hint") is not None:
            mapped_hint = self.map_timescopehint_to_versionhint(std_fields["time_scope_hint"])
            if mapped_hint is not None:
                std_fields["app_version_hint"] = mapped_hint
                # 根据映射后的 app_version_hint 设置 app_version（仅在 app_version 为 None 时）
                if mapped_hint in ["pre", "current", "next"] and std_fields.get("app_version") is None:
                    app_version = self.get_app_version_from_hint(mapped_hint)
                    if app_version is not None:
                        std_fields["app_version"] = app_version

        # 4、节点名称标准化
        if std_fields.get("node_name") and std_fields.get("node_name") is not None:
            app_version_hint = std_fields.get("app_version_hint")
            if app_version_hint in ["history", "pre", "current"]:
                # 查询的是tedi，按tedi命名规则格式化
                std_fields["node_name"] = self.format_tedi_node_name(std_fields["node_name"])
            elif app_version_hint in ["next", "future"]:
                # 查询的是iwiki，按iwiki命名规则格式化
                std_fields["node_name"] = self.format_iwiki_node_name(std_fields["node_name"])

        return std_fields

    def map_versionhint_to_versionhint(self, version_hint: str) -> str:
        """
        将版本号模糊描述映射为版本号类型
        """
        # 动态版本映射规则
        # app_version_hint 只是描述性词汇（如"当前版本"、"下一个版本"）
        # 具体数字版本号在 app_version 字段中，当 app_version 不为 None 时执行比较逻辑
        
        # 基础映射规则 - 使用if-elif格式方便增加新映射
        if version_hint in {"历史", "过去", "之前"}:
            return "history"
        elif version_hint in {"上一个","上个","上一个版本", "上一版本", "上个版本", "上版本"}:
            return "pre"
        elif version_hint in {"当前", "目前", "当前版本", "现在"}:
            return "current"
        elif version_hint in {"下一个", "下个","下一个版本", "下一版本","下个版本", "下版本"}:
            return "next"
        elif version_hint in {"未来", "将来", "后续"}:
            return "future"
        
        return None
    
    def get_version_type(self, app_version: str) -> str:
        """
        根据 app_version 与运行版本列表比较，返回版本类型
        """
        # 检查 app_version 是否为 None 或空字符串
        if not app_version:
            return None
            
        # 如果 app_version 是数字版本号，需要与运行版本列表比较
        if app_version.isdigit():
            query_version = int(app_version)
            
            # 将运行版本列表转换为数字列表
            current_versions_int = [int(v) for v in self.current_versions if v and v.isdigit()]
            
            if not current_versions_int:
                return None
            
            # 找到运行版本列表中的最大版本号
            max_current_version = max(current_versions_int)
            
            # 比较版本号
            if query_version in current_versions_int:
                return "current"  # 查询版本在当前运行版本列表中
            elif query_version > max_current_version:
                return "future"   # 查询版本大于当前运行版本
            else:
                return "history"  # 查询版本小于当前运行版本
        
        return None

    def map_timescopehint_to_versionhint(self, time_scope_hint: str) -> str:
        """
        将时间范围模糊描述映射为版本号类型
        """
        # 基础映射规则 - 使用if-elif格式方便增加新映射
        if time_scope_hint in {"历史", "过去", "之前", "以前", "早前"}:
            return "history"
        elif time_scope_hint in {"当前", "现在", "目前"}:
            return "current"
        elif time_scope_hint in {"未来", "将来", "后续", "以后", "之后"}:
            return "future"
        
        return None


    def get_app_version_from_hint(self, app_version_hint: str) -> str:
        """
        根据 app_version_hint 的值设置具体的 app_version
        """
        if not self.current_versions:
            return None
        
        # 将运行版本列表转换为数字列表
        current_versions_int = [int(v) for v in self.current_versions if v and v.isdigit()]
        if not current_versions_int:
            return None
        
        if app_version_hint == "pre":
            # 返回运行版本中的最小值减1
            return str(min (current_versions_int)- 1)
        elif app_version_hint == "current":
            # 返回运行版本中的最大值（当前版本）
            return str(max(current_versions_int))
        elif app_version_hint == "next":
            # 返回运行版本中的最大值加1
            return str(max(current_versions_int) + 1)
        
        return None

    def format_tedi_node_name(self, node_name: str) -> str:
        """
        按Tedi命名规则格式化节点名称
        用于history、pre、current版本的查询
        """
        # 检查 node_name 是否为 None 或空字符串
        if not node_name:
            return node_name
        
        # 删除包含"时间计划"或"阶段"的词汇
        node_name = node_name.replace("时间", "").replace("计划", "").replace("阶段", "").replace("节点", "").strip()
        
        # 处理数字灰度的模式匹配（一灰、二灰、三灰...）
        gray_pattern = r'^([一二三四五六七八九十]+)灰$'
        match = re.match(gray_pattern, node_name)
        if match:
            # 将中文数字转换为阿拉伯数字
            chinese_to_arabic = {
                '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
            }
            chinese_num = match.group(1)
            if chinese_num in chinese_to_arabic:
                arabic_num = chinese_to_arabic[chinese_num]
                return f"第{arabic_num}次灰度"

        if node_name in {"合流截止", "合流","截止合流"}:
            return "集成前准备"
        elif node_name in {"灰度上线前测试", "灰度上线前验证", "灰度上线前"}:
            return "灰度上线前验证"
        elif node_name in {"灰度实验", "灰度阶段", "实验灰度", "灰度时间", "灰度"}:
            return "灰度实验"
        elif node_name in {"版本上线前验证", "版本上线前测试"}:
            return "版本上线前测试"
        elif node_name in {"全量", "全量时间", "全量发布", "正式发布"}:
            return "正式发布配置"
        
        return node_name  # 不在映射表则原样返回

    def format_iwiki_node_name(self, node_name: str) -> str:
        """
        按iwiki命名规则格式化节点名称
        用于next、future版本的查询
        """
        # 检查 node_name 是否为 None 或空字符串
        if not node_name:
            return node_name
        
        # 删除包含"时间计划"或"阶段"的词汇
        node_name = node_name.replace("时间", "").replace("计划", "").replace("阶段", "").replace("节点", "").strip()
        
        # iwiki节点名称映射规则
        if node_name in {"合流截止", "合流","截止合流","集成前准备"}:
            return "合流截止"
        elif node_name in {"灰度上线前测试", "灰度上线前验证", "灰度上线前"}:
            return "灰度上线前验证"
        elif node_name in {"灰度实验", "灰度阶段", "实验灰度", "灰度时间", "灰度"}:
            return "实验灰度"
        elif node_name in {"全量", "全量时间", "全量发布", "正式发布","正式发布配置"}:
            return "版本发布"
        
        return node_name  # 不在映射表则原样返回

if __name__ == "__main__":
    tedi_client = TediClient()
    current_versions = tedi_client.get_running_versions_info()
    print("当前运行版本信息：", current_versions)
