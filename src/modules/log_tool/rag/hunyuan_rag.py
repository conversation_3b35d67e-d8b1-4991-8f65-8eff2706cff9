import os
import time
import uuid
import json
import requests


class HunyuanRAG:
    def __init__(self):
        self._rag_content = []

    # 从向量数据库索引RAG知识
    def emb_search(self, text, k, emb_index):
        ss_url = "http://stream-server-online-openapi.turbotke.production.polaris:8080/openapi/app_platform/emb_search"

        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer 7auGXNATFSKl7dF",
        }

        json_data = {
            "query_id": "test_query_id_" + str(uuid.uuid4()),
            "text": text,
            "k": k,
            "emb_index": emb_index,
        }
        resp = requests.post(ss_url, headers=headers, json=json_data)

        if resp.status_code == 200:
            resp = json.loads(resp.text)
            if resp['retcode'] == 0:
                self._rag_content = resp['results']
                print(self._rag_content)
                return self._rag_content


if __name__ == '__main__':
    text = 'retCode:-31'
    k = 3
    emb_index = "hyaide-application-4713-vdb-proxy"
    hunyuan_rag = HunyuanRAG()
    hunyuan_rag.emb_search(text, k, emb_index)
