import json
from typing import Optional, Dict, Any, Tuple
from datetime import datetime

from src.common.logs.logger import app_logger
from src.common.error.detailed_value_error import raise_value_error, ErrorCode
from src.client.hunyuan_client import HunyuanClient
from src.modules.log_tool.agent.filter_user_action import FilterUserAction
from src.client.fetch_log_client import FetchLogClient
from src.common.tools.file_utils import append_to_file
from src.client.iwiki_client import IWikiClient
from src.common.tools.text_parse_utils import TextParseUtils
from src.common.models.agent_result import AgentResult
from src.common.logs.loggers.event_logger import event_logger
from src.common.logs.loggers.models import Event
import tempfile


class LogAnalyzeAgent:
    def __init__(self):
        pass
    
    @staticmethod
    def process_log_analysis(extracted_params: Dict[str, Any], ticket_id: str, user_name: str, user_cn_name: str) -> AgentResult:
        """
        处理日志分析的完整流程
        
        Args:
            extracted_params: 从slot_filter提取的参数
            ticket_id: 工单ID
            user_name: 用户英文名
            user_cn_name: 用户中文名
            
        Returns:
            AgentResult: 日志分析结果
        """
        print("=" * 80)
        print("LogAnalyzeAgent.process_log_analysis 开始执行")
        print("=" * 80)
        
        try:
            # 判断是否需要重写prompt
            is_rewrite_prompt = extracted_params.get('is_rewrite_prompt', 'false')
            now = datetime.now()
            
            # 是否需要重写Prompt
            if is_rewrite_prompt == 'true':
                event = Event(
                    timestamp=now.strftime("%Y%m%d-%H%M%S"),
                    event_type="rewrite_prompt",
                    en_name=user_name,
                    cn_name=user_cn_name,
                    event_params={"ticket_id": ticket_id}
                )
                event_logger.write_event(event)
                # 重写Prompt，返回对应场景的iwiki链接
                user_query_scene, url = LogAnalyzeAgent.rewrite_prompt(
                    query=extracted_params.get('query', ''), 
                    ticket_id=ticket_id
                )
                content = f'已为你找到prompt文件链接，请按照文档里的模版提示改写：\n场景：{user_query_scene} -> [{url}]({url})'
                print(f"需要重写prompt，返回内容: {content}")
                return AgentResult.success(content)
            else:
                # 不重写，直接进行日志分析
                event = Event(
                    timestamp=now.strftime("%Y%m%d-%H%M%S"),
                    event_type="bot_analyze",
                    en_name=user_name,
                    cn_name=user_cn_name,
                    event_params={"ticket_id": ticket_id}
                )   
                event_logger.write_event(event)

                result_path = LogAnalyzeAgent.analyze_log(extracted_params, ticket_id)
                print(f"不需要重写prompt，返回文件路径: {result_path}")
                # 返回文件路径
                return AgentResult.success(result_path)
                
        except Exception as e:
            app_logger.error(f"日志分析处理失败：{e}")
            return AgentResult.error(str(e))

    @staticmethod
    def rewrite_prompt(query: str, ticket_id: Optional[str]) -> Tuple[str, str]:
        """
        重写prompt，获取场景和对应的iwiki链接
        
        :param query: 用户问题
        :param ticket_id: 工单ID
        :return: (user_query_scene, url)
        """
        print("=" * 80)
        print("LogAnalyzeAgent.rewrite_prompt 开始执行")
        print(f"查询问题: {query}")
        print(f"Ticket ID: {ticket_id}")
        print("=" * 80)
        
        # 查询配置文件，获取已有预设场景
        iwiki_client = IWikiClient()
        body_content = iwiki_client.get_doc_body("https://iwiki.woa.com/p/4014598562")
        model = "DeepSeek-V3-Online"
        scenes = []
        if body_content and body_content.lstrip().startswith('```'):
            # 提取代码块内容，得到json配置
            json_data = TextParseUtils.extract_code_block(body_content)
            json_items = TextParseUtils.parse_json_lines(json_data)
            scenes = [item.get('scene', '') for item in json_items]

        if not scenes:
            raise_value_error(ErrorCode.PRESET_SCENES_EMPTY, message="预设场景配置为空/未成功打开预设场景配置")
        
        # 增加其他场景
        scenes.append("其他")
        print(f"可用场景: {scenes}")

        # 用户意图场景及对应的 prompt iwiki 链接
        user_query_scene = ''
        url = ''
        user_intent_prompt = iwiki_client.get_doc_body("https://iwiki.woa.com/p/4014606396")
        if not (user_intent_prompt and user_intent_prompt.lstrip().startswith('```')):
            raise_value_error(ErrorCode.USER_INTENT_PROMPT_NOT_FOUND, message="未找到用户意图分析Prompt")

        user_intent_prompt = TextParseUtils.extract_code_block(user_intent_prompt)
        user_intent_prompt = user_intent_prompt.format(query=query, scenes=scenes)
        
        print(f"使用的模型: {model}")
        print(f"构建的prompt长度: {len(user_intent_prompt)}")
        print("=" * 80)

        items = LogAnalyzeAgent._request_model(user_intent_prompt, model)
        if items is None:
            print("流式结果为空")
            app_logger.info("流式结果为空")
            raise_value_error(ErrorCode.MODEL_STREAM_RESULT_EMPTY, message="流式结果为空")

        print("开始处理模型结果...")
        result_count = 0
        accumulated_data = ""
        has_results = False
        
        for result in items:
            has_results = True
            result_count += 1
            print(f"收到结果 {result_count}: type={result.get('type')}, data长度={len(result.get('data', ''))}")
            
            # 收集所有数据
            if result['type'] == 'thinking':
                accumulated_data += result['data']
            elif result['type'] == 'all_answer':
                accumulated_data += result['data']
        
        if not has_results:
            print("流式结果为空")
            app_logger.info("流式结果为空")
            raise_value_error(ErrorCode.MODEL_STREAM_RESULT_EMPTY, message="流式结果为空")
        
        print(f"累计数据: {accumulated_data}")
        
        # 尝试从累计数据中提取JSON
        try:
            data = TextParseUtils.extract_json_from_text(accumulated_data)
            print(f"解析的JSON数据: {data}")
            user_query_scene = data.get("issue_scene")
            print(f"识别到的场景: {user_query_scene}")
            
            if user_query_scene and user_query_scene != "其他":
                url = TextParseUtils.get_field_by_scene(json_items, user_query_scene, "iwiki_url")
                print(f"找到的URL: {url}")
                if ticket_id:
                    append_to_file(ticket_id, f'>>> 识别到的场景 以及 对应的prompt文件链接：\n{user_query_scene} -> {url}\n')
                return user_query_scene, url
            else:
                print(f"场景识别失败或为'其他': {user_query_scene}")
                raise_value_error(ErrorCode.USER_INTENT_SCENE_NOT_FOUND, message="未预设场景，请找 lichenlin 增加日志分析场景")
        except Exception as e:
            print(f"JSON解析失败: {str(e)}")
            print(f"原始数据: {accumulated_data}")
            raise

    @staticmethod
    def analyze_log(parsed_params: Dict[str, Any], ticket_id: str) -> str:
        """
        分析日志的主流程
        
        :param parsed_params: 已经解析好的参数字典
        :param ticket_id: 工单ID
        :return: 分析结果文件路径
        """
        
        # 下载并分析日志
        print(f"parsed_params: {parsed_params}")
        with tempfile.TemporaryDirectory() as tmpdirname:
            app_logger.info(f'临时文件夹路径: {tmpdirname}')
            
            # 获取日志文件路径
            fetch_log = FetchLogClient(
                download_link=parsed_params['log_link'], 
                log_save_path=tmpdirname
            )
            logs_path = fetch_log.fetch_log_from_url()

            if not logs_path:
                app_logger.info("未找到日志文件")
                return None
            
            print(f"11111111")

            # 分析日志
            app_logger.info("AI 开始分析您的日志...")
            try:
                filter_user_action = FilterUserAction(
                    query=parsed_params['query'],
                    logs_path=logs_path,  
                    bug_time=parsed_params.get('bug_time'),
                    ticket_id=ticket_id
                )
                result_stream = filter_user_action.parse_user_action()
                if not result_stream:
                    app_logger.info("日志返回结果为空 -- 混元模型过载，请重试")
                    raise_value_error(ErrorCode.MODEL_OVERLOAD, message="混元模型过载，请重试")

                # 处理流式结果
                result_answer_save_path = ''
                result_thinking_data = ''
                result_answer_data = ''
                
                for result in result_stream:
                    # 混元ds回包内容，思考为答案了
                    if (result['type'] == 'result_answer' and result['data']) or (result['type'] == 'all_thinking' and result['data']):
                        result_answer_data = result['data']
                        result_answer_save_path = LogAnalyzeAgent._save_result_to_file(result['data'], '日志分析结果')
                    
                    elif result['type'] == 'result_answer' and result['data'] is None:
                        app_logger.warning("日志分析结果为空 -- 日志分析失败")
                    
                    elif result['type'] == 'reasoning_content' and result['data'] == "混元模型过载，请重试":
                        result_answer_save_path = LogAnalyzeAgent._save_result_to_file("混元模型过载，请重试", '日志分析结果')
                    
                    else:
                        result_thinking_data += result['data']

                # 保存到工单文件
                append_to_file(ticket_id, f'>>> 思考过程\n{result_thinking_data}\n')
                append_to_file(ticket_id, f'>>> 回答内容\n{result_answer_data}\n')

                app_logger.info(f"日志分析完成，最终结果保存路径：{result_answer_save_path}")
                
                return result_answer_save_path
                
            except Exception as e:
                print(f"日志分析失败：bug_time: {parsed_params.get('bug_time')}")
                app_logger.error(f"日志分析失败：{e}")
                content = str(e)
                raise_value_error(ErrorCode.MODEL_OVERLOAD, message=content)

    @staticmethod
    def _save_result_to_file(result: str, path_suffix: str) -> str:
        """将分析结果保存为文件"""
        import os
        from datetime import datetime
        
        current_time = datetime.now().strftime("%Y-%m-%d %H-%M-%S-%f")
        output_dir = 'output/'
        os.makedirs(output_dir, exist_ok=True)
        save_path = os.path.join(output_dir, f'log_analyze_{path_suffix}_{current_time}.md')
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(result)
        return save_path

    @staticmethod
    def _request_model(prompt: str, model: str):
        """请求模型"""
        print("="*80)
        print("发送给模型的prompt内容：")
        print(prompt)
        print("="*80)
        app_logger.info(prompt)
        ss_url = 'http://api.taiji.woa.com/openapi/chat/completions'
        
        if "DeepSeek" in model:
            wsid = "11417"
            token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
            is_stream = True
            enable_enhancement = False

            hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream, enable_enhancement)
            yield from LogAnalyzeAgent._stream_results(hunyuan_client.request_deepseek(prompt))
        else:
            wsid = "10697"
            token = "00ac8819-7488-4487-bfbd-17f4d760aed8"
            is_stream = True
            enable_enhancement = True

            hunyuan_client = HunyuanClient(ss_url, wsid, model, token, is_stream, enable_enhancement)
            yield from LogAnalyzeAgent._stream_results(hunyuan_client.request(prompt))

    @staticmethod
    def _stream_results(results):
        """处理流式结果"""
        for result in results:
            yield {"data": result['data'], "type": result['type']}


if __name__ == "__main__":
    # 测试用例
    test_input = {
            "log_link": "https://cms.myapp.com/xy/yybtech/NGIT2mIu.zip",
            "query": "发货失败"
        }
    
    agent = LogAnalyzeAgent()
    try:
        result = agent.analyze_log(test_input, "test_ticket_001")
        print(f"分析结果: {result}")
    except Exception as e:
        print(f"分析失败: {str(e)}") 
